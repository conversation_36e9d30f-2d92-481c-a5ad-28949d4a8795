namespace TwentyFourDioGateway;

/// <summary>
/// DO轉AO輪詢背景服務
/// 持續輪詢遠端ModbusTCP Slave的Holding Register，並更新本地DO狀態
/// 支援斷線立即重連，無限重試不停止
/// </summary>
public class DoToAoPollingService : BackgroundService
{
    private readonly DoToAoService _doToAoService;
    private readonly ILogger<DoToAoPollingService> _logger;
    private readonly TimeSpan _pollingInterval = TimeSpan.FromSeconds(1); // 1秒輪詢一次
    private readonly TimeSpan _retryInterval = TimeSpan.FromSeconds(2); // 失敗後2秒重試

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="doToAoService">DO轉AO服務</param>
    /// <param name="logger">日誌記錄器</param>
    public DoToAoPollingService(DoToAoService doToAoService, ILogger<DoToAoPollingService> logger)
    {
        _doToAoService = doToAoService;
        _logger = logger;
    }

    /// <summary>
    /// 背景服務執行方法
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>執行任務</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting DO to AO polling service...");

        // 系統啟動時先執行一次強制同步，確保本地COIL狀態與遠端一致
        _logger.LogInformation("Performing initial force sync...");
        try
        {
            var syncSuccess = await _doToAoService.ForceSyncAllGroupsAsync();
            if (syncSuccess)
            {
                _logger.LogInformation("Initial force sync completed successfully");
            }
            else
            {
                _logger.LogWarning("Initial force sync completed with some failures, will continue with regular polling");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred during initial force sync, will continue with regular polling");
        }

        // 等待一小段時間後開始正常輪詢
        await Task.Delay(1000, stoppingToken);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var success = await _doToAoService.PollAllGroupsAsync();
                
                if (success)
                {
                    // 輪詢成功，等待正常間隔
                    await Task.Delay(_pollingInterval, stoppingToken);
                }
                else
                {
                    // 輪詢失敗，等待重試間隔
                    _logger.LogWarning("DO to AO polling failed, retrying in {RetryInterval}", _retryInterval);
                    await Task.Delay(_retryInterval, stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                // 正常停止，不記錄錯誤
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred during DO to AO polling");
                await Task.Delay(_retryInterval, stoppingToken);
            }
        }

        _logger.LogInformation("DO to AO polling service execution completed");
    }

    /// <summary>
    /// 服務停止時的清理工作
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任務</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping DO to AO polling service...");
        await base.StopAsync(cancellationToken);
        _logger.LogInformation("DO to AO polling service stopped");
    }
}
