﻿using System.Collections.Concurrent;
using System.Net.NetworkInformation;

namespace TwentyFourDioGateway.IcmpMonitor
{
    public class PingPool : IDisposable
    {
        private readonly ConcurrentBag<Ping> _pool;
        private readonly int _poolSize;

        public PingPool(int poolSize)
        {
            _poolSize = poolSize;
            _pool = new ConcurrentBag<Ping>();
            InitializePool();
        }

        private void InitializePool()
        {
            for (int i = 0; i < _poolSize; i++)
            {
                _pool.Add(new Ping());
            }
        }

        public Ping GetPing()
        {
            if (_pool.TryTake(out var ping))
            {
                return ping;
            }
            return new Ping(); // 如果池中沒有可用的 Ping，則創建一個新的（不建議池過小）。
        }

        public void ReturnPing(Ping ping)
        {
            _pool.Add(ping); // 將 Ping 實例返回到池中。
        }

        public void Dispose()
        {
            while (_pool.TryTake(out var ping))
            {
                ping.Dispose();
            }
        }
    }

}
