# TwentyFourDioGateway 物聯網閘道器系統使用指南

本指南針對 TwentyFourDioGateway 物聯網閘道器系統的最終用戶提供操作說明。

## 系統概述

TwentyFourDioGateway 是一個整合多種工業設備的物聯網閘道器，提供統一的 Modbus TCP 介面進行存取。系統可監控和控制：
- 數位輸入輸出(DIO)設備
- 安防監控攝影機
- 門禁系統
- 網路設備
- 醫療照護系統

## 系統基本需求

- Windows 作業系統
- .NET 7.0 Runtime
- 網路連接
- 適當的硬碟空間 (特別是啟用視頻錄影功能時)

## 系統啟動與停止

1. **啟動系統**：
   - 雙擊應用程式圖標或執行 `run.ps1` 腳本
   - 系統將自動載入配置並啟動所有已啟用的服務

2. **停止系統**：
   - 關閉應用程式窗口
   - 或按 Ctrl+C 結束命令行程序

## 系統配置

系統透過以下配置檔案控制功能：

### 1. setting.json

基本系統設定：
```json
{
  "Setting": {
    "ModbusTCPSlaveBindIpAddress": "192.168.x.x",  // Modbus TCP 服務器綁定 IP
    "ModbusTCPSlaveBindPort": 504,                 // Modbus TCP 服務器端口
    "EnablePingRongDawuNursing": false,            // 是否啟用大武護理站功能
    "EnableVideoRecording": true,                  // 是否啟用視頻錄影功能
    "EnableSafetyMessage": true,                   // 是否啟用安全訊息功能
    "EnableHundureIllegalCardNotification": true,   // 是否啟用非法卡片通知
    "AsteriskServer": {
      "Url": "http://your-asterisk-server:8088",
      "Username": "your-ari-username",
      "Password": "your-ari-password",
      "AsteriskUpdateIntervalMilliseconds": 1000
    }
  }
}
```

修改配置後請重啟系統使其生效。

### 2. device_mapping.json

詳細的設備配置，包括：

- DIO 設備列表與地址
- 攝影機設定
- 門禁系統配置
- 網路監控 IP 範圍
- 視頻錄影路徑與參數

### 3. dawu_settings.json (選用)

大武護理站相關配置，僅在啟用護理站功能時需要：
```json
{
  "Dawu": {
    "Nodes": [
      {
        "SipNumber": "Device01",                   // 設備 SIP 號碼
        "NurseStation": "HR",                      // 護理站識別碼
        "DisplayName": "護理站名稱",                // 顯示名稱
        "Mac": "AA:BB:CC:DD:EE:FF",               // MAC 地址
        "Ip": "192.168.x.x"                        // IP 地址
      }
    ]
  }
}
```

## 功能使用說明

### Modbus TCP 接口

系統提供標準 Modbus TCP 服務器，可通過任何支援 Modbus TCP 協議的客戶端進行存取：

- **IP 地址**：setting.json 中設定的 ModbusTCPSlaveBindIpAddress
- **端口**：setting.json 中設定的 ModbusTCPSlaveBindPort (預設 504)
- **支援功能**：
  - 讀取線圈狀態 (功能碼 01)
  - 讀取離散輸入 (功能碼 02)
  - 寫入單個線圈 (功能碼 05)
  - 寫入多個線圈 (功能碼 15)

### 各設備 Modbus 地址映射

系統將各類設備狀態映射到 Modbus 地址空間：

1. **DIO 設備**：
   - 數位輸入 (DI)：地址由 device_mapping.json 中設定
   - 數位輸出 (DO)：地址由 device_mapping.json 中設定

2. **攝影機系統**：
   - HiSharp AI 事件：地址由 HiSharpAiCameras.ModbusAddress 設定
   - Fuho 移動偵測：地址由 FuhoMotionDetectionCameras.ModbusAddress 設定

3. **門禁系統**：
   - 非法卡片警報：地址由 HundureCardReaderEndpoints.IllegalCardModbusAddress 設定

4. **網路設備監控**：
   - 設備狀態：地址由 PingIps.ModbusAddress 開始的連續地址

## 視頻錄影功能

系統支援根據事件自動錄製視頻：

1. **錄影觸發條件**：
   - HiSharp AI 攝影機事件
   - Fuho 移動偵測警報

2. **錄影參數**：
   - 存儲路徑：device_mapping.json 的 RecordPath 設定
   - 錄影時長：device_mapping.json 的 RecordTimeoutSeconds 設定

3. **訪問錄製的視頻**：
   - 本地檔案系統：設定的 RecordPath 目錄
   - 公開 URL：設定的 PublicVideoUrlPrefix 加上檔案名

## 系統監控與管理

系統運行日誌位於 logs 目錄，包含詳細的運行記錄和錯誤信息。

## 常見問題處理

1. **系統無法啟動**：
   - 檢查配置檔案是否正確
   - 確認 .NET Runtime 已正確安裝
   - 查看日誌目錄下的錯誤信息

2. **Modbus 連接問題**：
   - 確認 IP 地址和端口設定正確
   - 檢查防火牆是否阻擋了端口
   - 測試網路連通性

3. **設備狀態未更新**：
   - 確認設備 IP 地址配置正確
   - 檢查網路連接
   - 確認設備本身工作正常

4. **視頻錄影失敗**：
   - 確認硬碟空間充足
   - 檢查 FFmpeg 是否正確安裝
   - 確認攝影機設定正確且可訪問

5. **資料庫錯誤**：
   - 確認資料庫檔案未被損壞
   - 檢查資料庫目錄權限
   - 可能需要重建資料庫檔案

## 維護建議

1. **定期維護**：
   - 檢查並清理舊的視頻錄影檔案
   - 備份資料庫檔案
   - 檢查日誌文件大小並適時清理

2. **安全建議**：
   - 定期更改各設備的預設密碼
   - 確保系統在安全網路環境中運行
   - 限制只有授權人員可以訪問配置檔案

3. **系統更新**：
   - 定期檢查是否有系統更新版本
   - 更新前請先備份所有配置檔案和資料庫

## 技術支援

如有系統問題，請聯繫系統管理員或技術支援團隊。

## 功能與特色

### Asterisk 分機監控

本系統可與 Asterisk PBX 系統整合，監控分機狀態：

1. **分機在線狀態**: 顯示分機是否已註冊且可用
2. **通話狀態**: 檢測分機是否正在通話中
3. **Modbus 對應**: 自動將分機狀態對應至 Modbus DI (Discrete Input) 位址

#### 設定方式

1. 在 `setting.json` 中配置 Asterisk 連接：
   ```json
   "AsteriskServer": {
     "Url": "http://your-asterisk-server:8088",
     "Username": "your-ari-username",
     "Password": "your-ari-password",
     "AsteriskUpdateIntervalMilliseconds": 1000
   }
   ```

2. 在 `device_mapping.json` 中設定分機號碼與 Modbus DI 位址對應：
   ```json
   {
     "ExtensionMonitors": [
       {
         "Id": "分機監控1",
         "ExtensionNumber": "1000",
         "ModbusAddress": 100,
         "Description": "分機1000狀態監控"
       }
     ]
   }
   ```

> 注意：分機狀態會更新至 Modbus 的 DI (Discrete Input) 位址，以符合 Modbus 協定中唯讀輸入點的定義。

#### 問題排除

如果分機狀態監控不正確：

1. 檢查 Asterisk 伺服器連接設定是否正確
2. 確認分機號碼是否與 Asterisk 系統中的分機設定相符
3. 查看日誌檔案 `logs/debug.log` 了解詳細診斷資訊
4. 參考詳細說明文件: [Asterisk分機狀態識別](docs/2025-03-25-1515-asterisk-extension-status-fixes.md) 