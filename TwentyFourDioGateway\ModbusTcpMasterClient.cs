using System.Net.Sockets;

namespace TwentyFourDioGateway;

/// <summary>
/// ModbusTCP Master客戶端，用於DO轉AO功能
/// 自行實作ModbusTCP通信協議，確保資源自動釋放
/// </summary>
public class ModbusTcpMasterClient : IDisposable
{
    private readonly string _remoteIp;
    private readonly ushort _remotePort;
    private readonly byte _slaveId;
    private readonly ILogger<ModbusTcpMasterClient> _logger;
    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private readonly SemaphoreSlim _connectionSemaphore = new SemaphoreSlim(1, 1);
    private bool _disposed = false;
    private ushort _transactionId = 0;
    private DateTime _lastConnectTime = DateTime.MinValue;
    private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(5);
    private DateTime _lastErrorLogTime = DateTime.MinValue;
    private readonly TimeSpan _errorLogInterval = TimeSpan.FromSeconds(30); // 錯誤日誌間隔控制

    // 智慧型重試機制相關欄位
    private int _consecutiveFailures = 0;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private readonly int[] _retryDelaysMs = { 0, 100, 300, 500, 1000, 2000 }; // 漸進式延遲策略
    private DateTime _lastSuccessTime = DateTime.MinValue;

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="remoteIp">遠端IP位址</param>
    /// <param name="remotePort">遠端Port</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="logger">日誌記錄器</param>
    public ModbusTcpMasterClient(string remoteIp, ushort remotePort, byte slaveId, ILogger<ModbusTcpMasterClient> logger)
    {
        _remoteIp = remoteIp;
        _remotePort = remotePort;
        _slaveId = slaveId;
        _logger = logger;
    }

    /// <summary>
    /// 是否已連線
    /// </summary>
    public bool IsConnected
    {
        get
        {
            // 基本連線狀態檢查
            if (_tcpClient?.Connected != true || _networkStream == null)
                return false;

            // 進階連線狀態檢查 - 檢查 socket 是否真的可用
            try
            {
                // 使用 Poll 方法檢查 socket 狀態
                // Poll(0, SelectRead) 如果返回 true 且 Available = 0，表示連線已斷開
                if (_tcpClient.Client.Poll(0, SelectMode.SelectRead))
                {
                    return _tcpClient.Client.Available > 0; // 有資料可讀才是真正連線
                }
                return true; // Poll 返回 false 表示沒有待讀資料，連線正常
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// 連線到遠端ModbusTCP Slave
    /// </summary>
    /// <returns>連線是否成功</returns>
    public async Task<bool> ConnectAsync()
    {
        if (_disposed)
            return false;

        // 使用 SemaphoreSlim 確保同時只有一個連線操作
        await _connectionSemaphore.WaitAsync();
        try
        {
            // 如果已經連線，直接返回成功
            if (IsConnected)
            {
                _logger.LogDebug("Already connected to ModbusTCP Slave: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
                return true;
            }

            // 檢查重連間隔
            if (DateTime.Now - _lastConnectTime < _reconnectInterval)
            {
                _logger.LogDebug("Reconnection interval not reached, skipping connection: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
                return false;
            }

            _lastConnectTime = DateTime.Now;
        }
        finally
        {
            _connectionSemaphore.Release();
        }

        try
        {
            // 清理舊連線
            await DisconnectAsync();

            _logger.LogDebug("Connecting to ModbusTCP Slave: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);

            _tcpClient = new TcpClient();
            _tcpClient.ReceiveTimeout = 5000;
            _tcpClient.SendTimeout = 5000;

            await _tcpClient.ConnectAsync(_remoteIp, _remotePort);
            _networkStream = _tcpClient.GetStream();

            _logger.LogInformation("Successfully connected to ModbusTCP Slave: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to ModbusTCP Slave: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
            await DisconnectAsync();
            return false;
        }
    }

    /// <summary>
    /// 斷開連線
    /// </summary>
    public async Task DisconnectAsync()
    {
        try
        {
            // 使用 SemaphoreSlim 確保同時只有一個斷線操作
            await _connectionSemaphore.WaitAsync();
            try
            {
                _networkStream?.Close();
                _networkStream?.Dispose();
                _networkStream = null;

                _tcpClient?.Close();
                _tcpClient?.Dispose();
                _tcpClient = null;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception occurred while disconnecting ModbusTCP connection");
        }
    }

    /// <summary>
    /// 讀取Holding Register
    /// </summary>
    /// <param name="startAddress">起始位址</param>
    /// <param name="quantity">讀取數量</param>
    /// <returns>讀取的數值陣列，失敗時返回null</returns>
    public async Task<ushort[]?> ReadHoldingRegistersAsync(ushort startAddress, ushort quantity)
    {
        if (_disposed)
            return null;

        try
        {
            // 確保連線
            if (!IsConnected && !await ConnectAsync())
            {
                return null;
            }

            // 使用 SemaphoreSlim 確保同時只有一個讀取操作
            await _connectionSemaphore.WaitAsync();
            try
            {
                if (_networkStream == null)
                    return null;

                // 建立ModbusTCP請求封包
                var transactionId = ++_transactionId;
                var request = BuildReadHoldingRegistersRequest(transactionId, _slaveId, startAddress, quantity);

                // 使用 CancellationToken 設定 timeout (5秒)
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));

                // 發送請求
                await _networkStream.WriteAsync(request, 0, request.Length, cts.Token);

                // 讀取回應 - 分階段讀取以避免 timeout
                var headerBuffer = new byte[9]; // MBAP Header + Function Code + Byte Count
                var bytesRead = await ReadExactBytesAsync(_networkStream, headerBuffer, 0, headerBuffer.Length, cts.Token);

                if (bytesRead >= 9)
                {
                    // 檢查回應標頭
                    var responseTransactionId = (ushort)((headerBuffer[0] << 8) | headerBuffer[1]);
                    var byteCount = headerBuffer[8];

                    if (responseTransactionId == transactionId && headerBuffer[7] == 0x03 && byteCount == quantity * 2)
                    {
                        // 讀取資料部分
                        var dataBuffer = new byte[byteCount];
                        var dataBytesRead = await ReadExactBytesAsync(_networkStream, dataBuffer, 0, dataBuffer.Length, cts.Token);

                        if (dataBytesRead == byteCount)
                        {
                            // 解析數值
                            var values = new ushort[quantity];
                            for (int i = 0; i < quantity; i++)
                            {
                                values[i] = (ushort)((dataBuffer[i * 2] << 8) | dataBuffer[i * 2 + 1]);
                            }

                            //_logger.LogDebug("Successfully read Holding Register: StartAddress={StartAddress}, Quantity={Quantity}", startAddress, quantity);

                            // 記錄成功操作
                            RecordSuccess();
                            return values;
                        }
                    }
                }

                _logger.LogWarning("ModbusTCP read response validation failed: StartAddress={StartAddress}, Quantity={Quantity}", startAddress, quantity);

                // 回應驗證失敗，可能是連線問題，立即斷開連線
                await ForceDisconnectAndResetTimerAsync();
                return null;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
        catch (OperationCanceledException)
        {
            // Timeout 發生，記錄並強制重連
            _logger.LogWarning("ModbusTCP read operation timeout: StartAddress={StartAddress}, Quantity={Quantity}, Remote={RemoteIp}:{RemotePort}",
                startAddress, quantity, _remoteIp, _remotePort);

            await ForceDisconnectAndResetTimerAsync();
            return null;
        }
        catch (Exception ex)
        {
            // 控制錯誤日誌頻率，避免大量重複日誌
            var now = DateTime.Now;
            if (now - _lastErrorLogTime > _errorLogInterval)
            {
                _logger.LogError(ex, "Exception occurred while reading Holding Register: StartAddress={StartAddress}, Quantity={Quantity}, Remote={RemoteIp}:{RemotePort}",
                    startAddress, quantity, _remoteIp, _remotePort);
                _lastErrorLogTime = now;
            }
            else
            {
                // 在間隔期間只記錄簡單的警告
                _logger.LogWarning("Read failed (details suppressed): StartAddress={StartAddress}, Quantity={Quantity}, Remote={RemoteIp}:{RemotePort}",
                    startAddress, quantity, _remoteIp, _remotePort);
            }

            // 發生異常時，立即斷開連線並重設重連時間，使用智慧型延遲避免過度重試
            await ForceDisconnectAndResetTimerAsync();

            // 使用智慧型延遲策略
            await SmartDelayAsync();
            return null;
        }
    }

    /// <summary>
    /// 強制斷開連線並重設重連時間，允許立即重連
    /// </summary>
    private async Task ForceDisconnectAndResetTimerAsync()
    {
        try
        {
            await DisconnectAsync();
            // 重設重連時間，允許立即重連
            _lastConnectTime = DateTime.MinValue;

            // 記錄失敗並更新重試策略
            RecordFailure();

            _logger.LogDebug("Connection reset for immediate reconnection: {RemoteIp}:{RemotePort}, ConsecutiveFailures={ConsecutiveFailures}",
                _remoteIp, _remotePort, _consecutiveFailures);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception during force disconnect: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
        }
    }

    /// <summary>
    /// 記錄操作成功，重設失敗計數器
    /// </summary>
    private void RecordSuccess()
    {
        _consecutiveFailures = 0;
        _lastSuccessTime = DateTime.Now;
    }

    /// <summary>
    /// 記錄操作失敗，更新失敗計數器
    /// </summary>
    private void RecordFailure()
    {
        _consecutiveFailures++;
        _lastFailureTime = DateTime.Now;
    }

    /// <summary>
    /// 根據連續失敗次數計算智慧型延遲時間
    /// </summary>
    /// <returns>延遲時間（毫秒）</returns>
    private int GetSmartRetryDelay()
    {
        if (_consecutiveFailures == 0)
            return 0;

        // 使用漸進式延遲策略，但有上限
        int delayIndex = Math.Min(_consecutiveFailures - 1, _retryDelaysMs.Length - 1);
        return _retryDelaysMs[delayIndex];
    }

    /// <summary>
    /// 執行智慧型延遲，避免過度重試
    /// </summary>
    private async Task SmartDelayAsync()
    {
        int delayMs = GetSmartRetryDelay();
        if (delayMs > 0)
        {
            _logger.LogDebug("Smart retry delay: {DelayMs}ms after {ConsecutiveFailures} consecutive failures",
                delayMs, _consecutiveFailures);
            await Task.Delay(delayMs);
        }
    }

    /// <summary>
    /// 確保讀取指定數量的位元組，避免部分讀取問題
    /// </summary>
    /// <param name="stream">網路串流</param>
    /// <param name="buffer">緩衝區</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">要讀取的位元組數</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>實際讀取的位元組數</returns>
    private static async Task<int> ReadExactBytesAsync(NetworkStream stream, byte[] buffer, int offset, int count, CancellationToken cancellationToken)
    {
        int totalBytesRead = 0;
        int remainingBytes = count;

        while (remainingBytes > 0 && !cancellationToken.IsCancellationRequested)
        {
            int bytesRead = await stream.ReadAsync(buffer, offset + totalBytesRead, remainingBytes, cancellationToken);

            if (bytesRead == 0)
            {
                // 連線已關閉
                break;
            }

            totalBytesRead += bytesRead;
            remainingBytes -= bytesRead;
        }

        return totalBytesRead;
    }



    /// <summary>
    /// 寫入單一Holding Register
    /// </summary>
    /// <param name="address">暫存器位址</param>
    /// <param name="value">要寫入的值</param>
    /// <returns>寫入是否成功</returns>
    public async Task<bool> WriteSingleHoldingRegisterAsync(ushort address, ushort value)
    {
        if (_disposed)
            return false;

        try
        {
            // 確保連線
            if (!IsConnected && !await ConnectAsync())
            {
                return false;
            }

            // 使用 SemaphoreSlim 確保同時只有一個寫入操作
            await _connectionSemaphore.WaitAsync();
            try
            {
                if (_networkStream == null)
                    return false;

                // 建立ModbusTCP請求封包
                var transactionId = ++_transactionId;
                var request = BuildWriteSingleHoldingRegisterRequest(transactionId, _slaveId, address, value);

                // 使用 CancellationToken 設定 timeout (5秒)
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));

                // 發送請求
                await _networkStream.WriteAsync(request, 0, request.Length, cts.Token);

                // 讀取回應
                var response = new byte[12]; // ModbusTCP Write Single Holding Register回應長度
                var bytesRead = await ReadExactBytesAsync(_networkStream, response, 0, response.Length, cts.Token);

                if (bytesRead >= 12)
                {
                    // 驗證回應
                    if (ValidateWriteSingleHoldingRegisterResponse(response, transactionId, _slaveId, address, value))
                    {
                        _logger.LogDebug("Successfully wrote Holding Register: Address={Address}, Value={Value}", address, value);

                        // 記錄成功操作
                        RecordSuccess();
                        return true;
                    }
                }

                _logger.LogWarning("ModbusTCP write response validation failed: Address={Address}, Value={Value}", address, value);
                
                // 回應驗證失敗，可能是連線問題，立即斷開連線
                await ForceDisconnectAndResetTimerAsync();
                return false;
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
        catch (OperationCanceledException)
        {
            // Timeout 發生，記錄並強制重連
            _logger.LogWarning("ModbusTCP write operation timeout: Address={Address}, Value={Value}, Remote={RemoteIp}:{RemotePort}",
                address, value, _remoteIp, _remotePort);

            await ForceDisconnectAndResetTimerAsync();
            return false;
        }
        catch (Exception ex)
        {
            // 控制錯誤日誌頻率，避免大量重複日誌
            var now = DateTime.Now;
            if (now - _lastErrorLogTime > _errorLogInterval)
            {
                _logger.LogError(ex, "Exception occurred while writing Holding Register: Address={Address}, Value={Value}", address, value);
                _lastErrorLogTime = now;
            }
            else
            {
                // 在間隔期間只記錄簡單的警告
                _logger.LogWarning("Write failed (details suppressed): Address={Address}, Value={Value}", address, value);
            }
            
            // 發生異常時，立即斷開連線並重設重連時間，使用智慧型延遲避免過度重試
            await ForceDisconnectAndResetTimerAsync();

            // 使用智慧型延遲策略
            await SmartDelayAsync();
            return false;
        }
    }

    /// <summary>
    /// 建立讀取Holding Register的ModbusTCP請求封包
    /// </summary>
    private static byte[] BuildReadHoldingRegistersRequest(ushort transactionId, byte slaveId, ushort startAddress, ushort quantity)
    {
        var request = new byte[12];

        // Transaction ID (2 bytes)
        request[0] = (byte)(transactionId >> 8);
        request[1] = (byte)(transactionId & 0xFF);

        // Protocol ID (2 bytes) - 固定為0
        request[2] = 0x00;
        request[3] = 0x00;

        // Length (2 bytes) - 後續資料長度
        request[4] = 0x00;
        request[5] = 0x06;

        // Slave ID (1 byte)
        request[6] = slaveId;

        // Function Code (1 byte) - 0x03 = Read Holding Registers
        request[7] = 0x03;

        // Starting Address (2 bytes)
        request[8] = (byte)(startAddress >> 8);
        request[9] = (byte)(startAddress & 0xFF);

        // Quantity (2 bytes)
        request[10] = (byte)(quantity >> 8);
        request[11] = (byte)(quantity & 0xFF);

        return request;
    }

    /// <summary>
    /// 建立寫入單一Holding Register的ModbusTCP請求封包
    /// </summary>
    private static byte[] BuildWriteSingleHoldingRegisterRequest(ushort transactionId, byte slaveId, ushort address, ushort value)
    {
        var request = new byte[12];

        // Transaction ID (2 bytes)
        request[0] = (byte)(transactionId >> 8);
        request[1] = (byte)(transactionId & 0xFF);

        // Protocol ID (2 bytes) - 固定為0
        request[2] = 0x00;
        request[3] = 0x00;

        // Length (2 bytes) - 後續資料長度
        request[4] = 0x00;
        request[5] = 0x06;

        // Slave ID (1 byte)
        request[6] = slaveId;

        // Function Code (1 byte) - 0x06 = Write Single Holding Register
        request[7] = 0x06;

        // Register Address (2 bytes)
        request[8] = (byte)(address >> 8);
        request[9] = (byte)(address & 0xFF);

        // Register Value (2 bytes)
        request[10] = (byte)(value >> 8);
        request[11] = (byte)(value & 0xFF);

        return request;
    }

    /// <summary>
    /// 驗證寫入單一Holding Register的回應
    /// </summary>
    private static bool ValidateWriteSingleHoldingRegisterResponse(byte[] response, ushort expectedTransactionId, byte expectedSlaveId, ushort expectedAddress, ushort expectedValue)
    {
        if (response.Length < 12)
            return false;

        // 檢查Transaction ID
        var transactionId = (ushort)((response[0] << 8) | response[1]);
        if (transactionId != expectedTransactionId)
            return false;

        // 檢查Protocol ID
        if (response[2] != 0x00 || response[3] != 0x00)
            return false;

        // 檢查Length
        var length = (ushort)((response[4] << 8) | response[5]);
        if (length != 0x06)
            return false;

        // 檢查Slave ID
        if (response[6] != expectedSlaveId)
            return false;

        // 檢查Function Code
        if (response[7] != 0x06)
            return false;

        // 檢查Address
        var address = (ushort)((response[8] << 8) | response[9]);
        if (address != expectedAddress)
            return false;

        // 檢查Value
        var value = (ushort)((response[10] << 8) | response[11]);
        if (value != expectedValue)
            return false;

        return true;
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            DisconnectAsync().Wait();
            _connectionSemaphore.Dispose();
            _disposed = true;
        }
    }
}
