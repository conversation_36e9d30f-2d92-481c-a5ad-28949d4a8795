﻿using System.Threading.Channels;

namespace TwentyFourDioGateway.DawuNursing.Model
{
    public class DawuSetting
    {

        public List<SubnetChannel> SubnetChannels { get; set; } = new List<SubnetChannel>();
        //public string SourceIp { get; set; } = string.Empty;
        //public ushort SourcePort { get; set; }
        //public string SourceMac { get; set; } = string.Empty;
        //public string TargetIp { get; set; } = string.Empty;
        //public ushort TargetPort { get; set; } = 2269;
        //public string ResponseIp { get; set; } = string.Empty;
        //public ushort ResponsePort { get; set; } = 2270;
        public ushort ResponseTimeoutSeconds { get; set; } = 30;
        public ushort SendTimeoutMilliseconds { get; set; } = 200;
        public List<NodeModel> Nodes { get; set; } = new List<NodeModel>();
        public List<NurseStation> NurseStations { get; set; } = new List<NurseStation>();
    }
}
