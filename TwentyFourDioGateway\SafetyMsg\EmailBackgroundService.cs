using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Net;
using System.Text;
using System.Threading.Channels;
using System.Threading.Tasks;

namespace TwentyFourDioGateway.SafetyMsg
{
    /// <summary>
    /// 背景郵件處理服務，負責從Channel中讀取並發送郵件
    /// </summary>
    public class EmailBackgroundService : BackgroundService
    {
        private readonly ILogger<EmailBackgroundService> _logger;
        private readonly Channel<EmailMessage> _emailChannel;
        private readonly SmtpClient _smtpClient;
        
        private const string EMAIL_SENDER = "<EMAIL>";
        private const string EMAIL_PASSWORD = "ryaqinvkowdqgqhf";

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="logger">日誌服務</param>
        /// <param name="emailChannel">郵件佇列Channel</param>
        public EmailBackgroundService(
            ILogger<EmailBackgroundService> logger,
            Channel<EmailMessage> emailChannel)
        {
            _logger = logger;
            _emailChannel = emailChannel;
            
            // 初始化SMTP客戶端
            _smtpClient = new SmtpClient("smtp.gmail.com", 587)
            {
                Credentials = new NetworkCredential(EMAIL_SENDER, EMAIL_PASSWORD),
                EnableSsl = true
            };
        }

        /// <summary>
        /// 背景服務執行方法
        /// </summary>
        /// <param name="stoppingToken">取消標記</param>
        /// <returns>異步任務</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("郵件背景處理服務已啟動");

            await foreach (var emailMessage in _emailChannel.Reader.ReadAllAsync(stoppingToken))
            {
                try
                {
                    await SendEmailAsync(emailMessage, stoppingToken);
                    _logger.LogInformation("成功發送郵件至 {Recipient}，主旨: {Subject}", 
                        emailMessage.Recipient, emailMessage.Subject);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "發送郵件至 {Recipient} 失敗，主旨: {Subject}", 
                        emailMessage.Recipient, emailMessage.Subject);
                    
                    // 重試邏輯
                    if (emailMessage.RetryCount < EmailMessage.MaxRetryCount)
                    {
                        emailMessage.RetryCount++;
                        _logger.LogWarning("將重試發送郵件至 {Recipient}，重試次數: {RetryCount}/{MaxRetryCount}", 
                            emailMessage.Recipient, emailMessage.RetryCount, EmailMessage.MaxRetryCount);
                        
                        // 延遲一段時間後重新入佇列
                        await Task.Delay(TimeSpan.FromSeconds(10 * emailMessage.RetryCount), stoppingToken);
                        await _emailChannel.Writer.WriteAsync(emailMessage, stoppingToken);
                    }
                    else
                    {
                        _logger.LogError("郵件發送至 {Recipient} 已達最大重試次數，放棄發送", emailMessage.Recipient);
                    }
                }
            }
        }

        /// <summary>
        /// 發送單一郵件
        /// </summary>
        /// <param name="emailMessage">郵件訊息</param>
        /// <param name="cancellationToken">取消標記</param>
        /// <returns>異步任務</returns>
        private async Task SendEmailAsync(EmailMessage emailMessage, CancellationToken cancellationToken)
        {
            var mailMessage = new MailMessage
            {
                From = new MailAddress(EMAIL_SENDER),
                Subject = emailMessage.Subject,
                Body = emailMessage.Body,
                IsBodyHtml = false
            };
            
            mailMessage.To.Add(emailMessage.Recipient);
            await _smtpClient.SendMailAsync(mailMessage, cancellationToken);
        }
    }
} 