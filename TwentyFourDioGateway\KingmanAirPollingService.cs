namespace TwentyFourDioGateway;

/// <summary>
/// 空調狀態輪詢背景服務
/// 定期輪詢空調狀態並更新ModbusTCP Slave的DI點位
/// </summary>
public class KingmanAirPollingService : BackgroundService
{
    private readonly KingmanAirService _kingmanAirService;
    private readonly ILogger<KingmanAirPollingService> _logger;
    private readonly TimeSpan _pollingInterval = TimeSpan.FromSeconds(5); // 輪詢間隔
    private readonly TimeSpan _retryInterval = TimeSpan.FromSeconds(10); // 失敗重試間隔

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="kingmanAirService">空調服務</param>
    /// <param name="logger">日誌記錄器</param>
    public KingmanAirPollingService(
        KingmanAirService kingmanAirService,
        ILogger<KingmanAirPollingService> logger)
    {
        _kingmanAirService = kingmanAirService;
        _logger = logger;
    }

    /// <summary>
    /// 執行輪詢任務
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>任務</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Kingman Air polling service started with interval: {PollingInterval} seconds", _pollingInterval.TotalSeconds);

        // 無限循環，直到服務停止
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 執行輪詢
                var success = await _kingmanAirService.PollAllGroupsAsync();

                
                    await Task.Delay(_pollingInterval, stoppingToken);
                //if (success)
                //{
                    // 輪詢成功，等待下次輪詢間隔
                //}
               // else
                //{
                    // 輪詢失敗，較短間隔後重試
                  //  _logger.LogWarning("Kingman Air polling failed, retrying in {RetryInterval} seconds", _retryInterval.TotalSeconds);
                   // await Task.Delay(_retryInterval, stoppingToken);
                //}
            
            }
            catch (OperationCanceledException)
            {
                // 服務正在停止，正常退出
                _logger.LogInformation("Kingman Air polling service received stop signal");
                break;
            }
            catch (Exception ex)
            {
                // 記錄異常並繼續運行
                _logger.LogError(ex, "Exception occurred in Kingman Air polling service");
                await Task.Delay(_retryInterval, stoppingToken);
            }
        }

        _logger.LogInformation("Kingman Air polling service stopped");
    }

    /// <summary>
    /// 停止服務
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任務</returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Kingman Air polling service...");
        await base.StopAsync(cancellationToken);
    }
}
