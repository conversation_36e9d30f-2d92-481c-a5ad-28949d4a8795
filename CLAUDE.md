# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Memory

- 請永遠用中文回答
- 註解請不要只有寫物件的名稱，那不如不要寫
- 每一次的修改都要確認可以編譯得過，且可以正常運行起來

## Project Overview

TwentyFourDioGateway is an industrial IoT gateway system built with .NET 9.0 that integrates multiple industrial devices, security systems, and healthcare systems through a unified Modbus TCP interface. The system serves as a central hub for controlling and monitoring various hardware devices including 24-channel digital I/O controllers, IP cameras, HVAC systems, door access control, and medical care stations.

## Build and Run Commands

```bash
# Build the project
dotnet build

# Run the application
dotnet run --project TwentyFourDioGateway/TwentyFourDioGateway.csproj

# Run using the provided PowerShell script (Windows)
./TwentyFourDioGateway/run.ps1

# Apply database migrations
dotnet ef database update --project TwentyFourDioGateway/TwentyFourDioGateway.csproj

# Create new migration
dotnet ef migrations add <MigrationName> --project TwentyFourDioGateway/TwentyFourDioGateway.csproj
```

## Testing

⚠️ **No test framework is currently set up.** The project lacks automated tests. To add testing:

```bash
# Create test project (if needed)
dotnet new xunit -n TwentyFourDioGateway.Tests
dotnet sln add TwentyFourDioGateway.Tests
dotnet add TwentyFourDioGateway.Tests reference TwentyFourDioGateway

# Run tests (once implemented)
dotnet test
```

## Architecture

### Core Service Architecture
The system uses a layered architecture with background services handling device communication:

- **GatewayService**: Central service managing Modbus data store and device state synchronization
- **ModbusTcpServerService**: Provides Modbus TCP Slave interface for external systems
- **TwentyFourDioPollingService**: Manages communication with 24-channel DIO devices
- **VideoRecordingHostedService**: Handles event-triggered video recording from IP cameras
- **Background Services**: Multiple specialized services for different device types (HVAC, door access, network monitoring)

### Data Flow
1. **Device Polling**: Background services continuously poll hardware devices
2. **State Management**: GatewayService maintains in-memory data store synchronized with SQLite database
3. **Modbus Interface**: External systems interact via standard Modbus TCP protocol
4. **Event Processing**: Device state changes trigger automated responses (video recording, notifications)

### Configuration System
The system uses three main JSON configuration files:
- `setting.json`: System-wide feature flags and connection settings
- `device_mapping.json`: Device IP addresses, Modbus address mappings, and hardware configurations
- `dawu_settings.json`: Healthcare system specific settings (optional)

### Device Integration Modules
- **24DIO Controllers**: Digital input/output device management with configurable Modbus address mapping
- **IP Cameras**: HiSharp and Fuho camera integration with AI event processing and video recording
- **HVAC Systems**: Kingman air conditioning control with DO-to-AO conversion
- **Door Access**: Hundure card reader system with illegal card notifications
- **Network Monitoring**: ICMP ping monitoring with batch processing for network device status
- **Healthcare**: Dawu nursing station integration for medical facility monitoring
- **Asterisk PBX**: Extension status monitoring with configurable range mapping

## Key Files and Their Purposes

- `Program.cs:1-621`: Application entry point with comprehensive service registration and API endpoint configuration
- `GatewayService.cs`: Core service managing Modbus data store and cross-device state synchronization
- `TwentyFourDioMaster.cs`: Primary controller for DIO device communication and state management
- `ModbusTcpServer.cs`: Modbus TCP protocol implementation using NModbus4.Core
- `TwentyFourDioPollingEngine.cs`: High-frequency polling engine for real-time device monitoring
- `DoToAoService.cs`: Handles conversion between digital output and analog output signals
- `VideoRecordingService.cs`: Manages FFmpeg-based video recording with channel-based queuing

## Development Notes

- **Database**: Uses SQLite with Entity Framework Core migrations for state persistence
- **Logging**: Comprehensive Serilog configuration with separate log files for different severity levels
- **Configuration Hot-Reload**: JSON configuration files support runtime reloading
- **API Documentation**: Swagger UI available at `/swagger` endpoint
- **Cross-Origin**: CORS configured with "AllowAll" policy for web interface integration
- **Service Lifecycle**: Most device services are registered as hosted services with automatic startup/shutdown
- **Error Handling**: Extensive exception handling with structured logging throughout the codebase

## Common Modbus Address Mappings

The system uses standard Modbus addressing:
- **Coils (DO)**: Digital outputs for device control
- **Discrete Inputs (DI)**: Digital inputs for sensor reading
- **Address ranges are configurable** via `device_mapping.json` for different device types and installations

Configuration changes require application restart to take effect.