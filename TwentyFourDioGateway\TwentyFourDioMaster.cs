﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace TwentyFourDioGateway;
public class TwentyFourDioMaster
{
    public IPEndPoint endpoint;
    public bool IsConnected { get => tcpClient != null ? tcpClient.Connected : false; }
    private TcpClient? tcpClient;
    private NetworkStream? networkStream;
    public StreamWriter? sw;
    public StreamReader? sr;
    public string Ip { get; set; }
    List<DateTime> _lastDiUpdateTimes = new List<DateTime>();
    List<DateTime> _lastDoUpdateTimes = new List<DateTime>();
    public ushort Port { get; set; }
    private List<Task> tasks = new List<Task>();
    private CancellationToken _token;
    private DateTime lastTime = DateTime.Now;
    private bool Reconnected = false;
    public string Id;
    public Func<string, Task>? DataReceivedCallBack { get; set; }
    public Func<string, Task>? DataChangedCallBack { get; set; }
    public readonly ConcurrentQueue<(byte Index, bool Value)?> CommandQueue = new ConcurrentQueue<(byte Index, bool Value)?>();
    private readonly ILogger<TwentyFourDioMaster> logger;
    private char[] _diSnapshot { get; set; } = new char[29];
    private char[] _doSnapshot { get; set; } = new char[13];
    
    public TwentyFourDioMaster(ILogger<TwentyFourDioMaster> logger, string id, IPEndPoint endPoint, CancellationToken token)
    {
        this.logger = logger;
        Array.Fill<char>(_diSnapshot, '0');
        Array.Fill<char>(_doSnapshot, '0');
        //_diSnapshot[0] = 'D';
        //_diSnapshot[28] = '\r';
        //_doSnapshot[12] = '\r';
        //Id = id;
        this.endpoint = endPoint;
        Ip = endpoint.Address.ToString();
        Port = (ushort)endpoint.Port;
        this._token = token;
        lastTime = DateTime.Now;
        //tcpClient = new TcpClient();
        tasks = new List<Task>() { 
            Task.Run(() => backgroundRead(token)), 
            Task.Run(() => backgroundCommand(token)),
            Task.Run(() => CheckConnectionState(token))};
    }
    private async Task CheckConnectionState(CancellationToken token)
    {
        while (true)
        {
            try
            {
                if ((DateTime.Now - lastTime).TotalSeconds > 15)
                {
                    //if (!IsConnected || !Reconnected)
                    {
                        logger.LogInformation($"Disconnected: {Ip}@{Port} IsConnected:{IsConnected}, Reconnected: {Reconnected}");
                        Disconnect();
                        Reconnected = false;
                        for (int i = 0; i < 30; i++)
                        {
                            if (token.IsCancellationRequested)
                            {
                                return;
                            }
                            await Task.Delay(1000);
                        }
                        lastTime = DateTime.Now;
                    }

                }
                //for (int i = 0; i < 10; i++)
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }
                    await Task.Delay(1000);
                }
            }
            catch (Exception)
            {

            }

        }
    }
    public async Task<bool> ConnectAsync()
    {
        tcpClient = new TcpClient();
        try
        {
            var connectTask = tcpClient.Client.ConnectAsync(endpoint, new CancellationTokenSource(5000).Token);
            await connectTask;
            
        }
        catch (OperationCanceledException e)
        {
            Disconnect();
            return false;
        }
        if (tcpClient.Connected)
        {
            logger.LogInformation($"Connected: {Ip}@{Port}");
            Reconnected = true;

            networkStream = new NetworkStream(tcpClient.Client);
            sw = new StreamWriter(networkStream);
            sr = new StreamReader(networkStream);
            sw.AutoFlush = true;
            //if (tasks.Any())
            //{
            //    tasksCts.Cancel();
            //    await Task.WhenAll(tasks);
            //}
            //tasksCts = new CancellationTokenSource();
            
        }
        return tcpClient.Connected;
    }
    public async Task backgroundCommand(CancellationToken cts)
    {
        while (!cts.IsCancellationRequested)
        {
            try
            {
                if (IsConnected && networkStream != null)
                {                    
                    await GetDoAsync();
                    //await Task.Delay(50);                    
                    await CheckAndSendCommandAsync(cts);
                    await sw.FlushAsync();
                    await Task.Delay(50);
                    await GetDiAsync();
                    await sw.FlushAsync();
                    await CheckAndSendCommandAsync(cts);
                    await sw.FlushAsync();
                    //await Task.Delay(50);
                }
                await Task.Delay(50);
            }
            catch (Exception e)
            {
                await Task.Delay(1000);
            }
         
        }
    }
    public async Task CheckAndSendCommandAsync(CancellationToken cts)
    {
        if (CommandQueue.TryPeek(out var peek))
        {
            while (CommandQueue.TryDequeue(out var item))
            {
                await Task.Delay(50);
                //Console.WriteLine("DequeuedMaster");
                if (cts.IsCancellationRequested)
                {
                    break;
                }
                string cmd = string.Empty;
                if (item.Value.Value)
                {
                    cmd = $"@SET DO {Enumerable.Range(0, 8).Select(e => e == item.Value.Index ? "1" : "0").Aggregate((p, n) => $"{p}{n}")} " + (char)0x0d;
                }
                else
                {
                    cmd = $"@CLR DO {Enumerable.Range(0, 8).Select(e => e == item.Value.Index ? "1" : "0").Aggregate((p, n) => $"{p}{n}")} " + (char)0x0d;
                }
                logger.LogInformation($"{Id} Command:{cmd}");
                await sw.WriteAsync(cmd);
                item = null;
            }
        }


    }

    public bool CompareDiAndSetNewStatusAndDebounceAsync(char[] newStatuses)
    {
        var now = DateTime.Now;
        var changed = false;
        for (byte i = 0; i < _diSnapshot.Length; i++)
        {
            var result = _diSnapshot[i] == newStatuses[i];
            if (!result && (now - _lastDiUpdateTimes[i]).TotalMilliseconds > 500)
            {
                _lastDiUpdateTimes[i] = now;
                _diSnapshot[i] = newStatuses[i];                
                changed = true;
            }
        }
        return changed;
    }
    public async Task backgroundRead(CancellationToken cts)
    {
    
        var buf = new char[50];
        Stopwatch sw = new Stopwatch();

        for (int i = 0; i < 29; i++)
        {
            _lastDiUpdateTimes.Add(DateTime.Now.AddMinutes(-1));
        }
        for (int i = 0; i < 13; i++)
        {
            _lastDoUpdateTimes.Add(DateTime.Now.AddMinutes(-1));
        }
        while (!cts.IsCancellationRequested)
        {
            try
            {
                if (IsConnected && networkStream != null)
                {
                    if (networkStream.DataAvailable)
                    {
                        lastTime = DateTime.Now;
                        var index = 0;
                        sw = Stopwatch.StartNew();
                        //while (await sr!.ReadAsync(buf, index, 1) > 0 && buf[index0] != '\r' && sw.ElapsedMilliseconds < 100)
                        //{
                        //    index++;
                        //}
                        do
                        {
                            if (sw.ElapsedMilliseconds > 2000)
                            {
                                break;
                            }
                        } while (await sr.ReadAsync(buf, index, 1) > 0 && buf[index++] != '\r'/* && sw.ElapsedMilliseconds < 150*/);

                        sw.Stop();
                        //Incomplete data
                        if (buf[index - 1] != '\r')
                        {
                            logger.LogWarning($"Incomplete data  {Ip}@{Port}: {new string(buf.Take(index).ToArray())}");
                            await networkStream.FlushAsync();
                            continue;
                        }

                        var result = buf.Take(index).ToArray();
                        var dataChanged = false;
                        if (result[0] == '@' && result[1] == 'D' && result[2] == 'I')
                        {
                            if (!_diSnapshot.SequenceEqual(result))
                            {
                                dataChanged = CompareDiAndSetNewStatusAndDebounceAsync(result);
                                //dataChanged = true;
                                //_diSnapshot = result;
                            }
                        }

                        if (result[0] == '@' && result[1] == 'D' && result[2] == 'O')
                        {
                            if (!_doSnapshot.SequenceEqual(result))
                            {
                                dataChanged = true;
                                _doSnapshot = result;
                            }
                        }

                        if (DataReceivedCallBack != null)
                        {
                            await DataReceivedCallBack.Invoke(new string(result));
                            //await DataReceivedCallBack.Invoke($"{Ip}@{Port} :{new string(result)}");
                        }
                        if (dataChanged && DataChangedCallBack != null)
                        {
                            await DataChangedCallBack.Invoke(new string(result));
                            //await DataChangedCallBack.Invoke($"{Ip}@{Port} :{new string(result)}");
                        }
                    }

                }
                await Task.Delay(10);
            }
            catch (Exception e)
            {

            }
         
        }
    }
    public void Disconnect()
    {
        if (tcpClient != null)
        {
            if (tcpClient.Connected)
            {
                tcpClient.GetStream().Close();
            }
            //tcpClient.Client.Disconnect(true);

            tcpClient.Close();
            tcpClient = null;
            //return !tcpClient.Connected;
        }
        //return false;
    }
    //public async Task<IEnumerable<bool>> PassiveGetDiAsync(CancellationToken cts)
    //{

    //    try
    //    {
    //        char[] bufs = new char[29];            
    //        //StreamReader ssr = new StreamReader(networkStream);
            
    //        //var result = await readTask;
    //        //networkStream = new NetworkStream(tcpClient.Client);
    //        while (!cts.IsCancellationRequested)
    //        {
    //            if (networkStream.DataAvailable)
    //            {
    //                //StreamReader ssr = new StreamReader(networkStream);
    //                await sr.ReadBlockAsync(bufs);
    //                var result = bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    //                Console.WriteLine($"{result.Select(e => e ? "1" : "0").Aggregate((p, n) => p + n)}");    static void OnProcessExit (object sender, EventArgs e)

    //                return result;
    //            }
    //        }
    //        //while (true)
    //        //{          
    //        //    if (readTask.IsCompletedSuccessfully)
    //        //    {
    //        //        var result = bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    //        //        Console.WriteLine($"{result.Select(e => e ? "1" : "0").Aggregate((p, n) => p + n)}");
    //        //        return result;
    //        //    }
    //        //    else if (readTask.IsCanceled)
    //        //    {
    //        //        Console.WriteLine("Failed");
    //        //        break;
    //        //    }
    //        //    await Task.Delay(100);
    //        //}

    //    }
    //    catch (Exception e)
    //    {

    //    }

    //    return Enumerable.Empty<bool>();
    //}
    public async Task GetDiAsync()
    {
        //char[] bufs = new char[29];
        //await networkStream.FlushAsync();
        //var readTask = sr.ReadBlockAsync(bufs, 0, 29);
        if (IsConnected)
        {
            CancellationTokenSource cts = new CancellationTokenSource(1000);
            StringBuilder sb = new StringBuilder("@GET DI\r");
        
            await sw.WriteAsync(sb,cts.Token);
        }
        
        //var result = await readTask;
        //await networkStream.FlushAsync();
        //return bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    }

    public async Task GetDoAsync()
    {
        if (IsConnected)
        {
            CancellationTokenSource cts = new CancellationTokenSource(1000);
            StringBuilder sb = new StringBuilder("@GET DO\r");

            await sw.WriteAsync(sb,cts.Token);

        }
    }

    public async Task PinDoAsync(byte Data)
    {
        //char[] bufs = new char[13];
        //var readTask = sr.ReadBlockAsync(bufs, 0, 13);
        var command = Enumerable.Range(0, 8).Select(e => (Data & (0x01 << (7 - e))) != 0 ? '1' : '0').ToArray();

        
        await sw.WriteAsync($"@PIN DO {new string(command)}\r");
        //return Enumerable.Empty<bool>();
        //var result = await readTask;
        //return bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    }

    public async Task SetDoAsync(List<byte> bits)
    {
        //char[] bufs = new char[13];
        //var readTask = sr.ReadBlockAsync(bufs, 0, 13);
        char[] chars = Enumerable.Repeat('0', 8).ToArray();
        foreach (var bit in bits)
        {
            chars[7 - (bit - 1)] = '1';
        }
        await sw.WriteAsync($"@SET DO {new string(chars)}\r");
        //var result = await readTask;
        //return bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    }

    public async Task ClrDoAsync(List<byte> bits)
    {
        //char[] bufs = new char[13];
        //var readTask = sr.ReadBlockAsync(bufs, 0, 13);
        char[] chars = Enumerable.Repeat('0', 8).ToArray();
        foreach (var bit in bits)
        {
            chars[7 - (bit - 1)] = '1';
        }
        await sw.WriteAsync($"@CLR DO {new string(chars)}\r");
        //var result = await readTask;
        //return bufs.Skip(4).Select(e => e == '1').SkipLast(1).ToList();
    }
}