# DoToAo 功能測試指南

## 🔧 修改內容總結

### 1. 啟用 DoToAo 功能
- 修改 `setting.json` 中的 `EnableDoToAo` 為 `true`

### 2. 修正連線狀態檢查邏輯
- 修正 `ModbusTcpMasterClient.IsConnected` 屬性的邏輯錯誤
- 原本的 Poll 檢查邏輯會導致未連線時也返回 true

### 3. 智慧型重試機制
- 添加漸進式延遲策略：0ms → 100ms → 300ms → 500ms → 1000ms → 2000ms
- 添加 timeout 處理機制（5秒）
- 強化異常處理和重連邏輯

## 🧪 測試步驟

### 1. 檢查服務啟動
啟動應用程式後，檢查日誌中是否有以下訊息：
```
Starting DO to AO polling service...
Performing initial force sync...
```

### 2. 檢查連線狀態
使用 API 檢查連線狀態：
```bash
curl http://localhost:5000/api/do-to-ao/status
```

預期回應：
```json
{
  "測試群組1 (***************:503:1)": false
}
```

### 3. 手動觸發輪詢測試
```bash
curl -X POST http://localhost:5000/api/do-to-ao/poll
```

### 4. 手動測試 DO 轉 AO
```bash
curl -X POST "http://localhost:5000/api/do-to-ao/test?doAddress=900&value=true"
curl -X POST "http://localhost:5000/api/do-to-ao/test?doAddress=900&value=false"
```

### 5. 手動重新連線測試
```bash
curl -X POST http://localhost:5000/api/do-to-ao/reconnect
```

## 📋 預期行為

### 正常情況
1. **服務啟動**：DoToAoPollingService 應該正常啟動
2. **初始同步**：執行 ForceSyncAllGroupsAsync 進行初始同步
3. **定期輪詢**：每 1 秒輪詢一次遠端 Holding Register
4. **連線管理**：自動檢測連線狀態並重連

### 連線失敗情況
1. **第一次失敗**：立即重試（0ms 延遲）
2. **連續失敗**：使用漸進式延遲
3. **Timeout**：5 秒後強制斷線重連
4. **日誌記錄**：詳細記錄連線狀態和重試資訊

## 🔍 日誌監控

### 關鍵日誌訊息
```
[DoToAoPollingService] Starting DO to AO polling service...
[DoToAoService] Creating new ModbusTCP Master client: Group=測試群組1
[ModbusTcpMasterClient] Connecting to ModbusTCP Slave: ***************:503
[ModbusTcpMasterClient] Successfully connected to ModbusTCP Slave: ***************:503
```

### 錯誤日誌訊息
```
[ModbusTcpMasterClient] Failed to connect to ModbusTCP Slave: ***************:503
[ModbusTcpMasterClient] ModbusTCP read operation timeout
[ModbusTcpMasterClient] Smart retry delay: 100ms after 1 consecutive failures
```

## 🐛 故障排除

### 1. 服務未啟動
- 檢查 `setting.json` 中 `EnableDoToAo` 是否為 `true`
- 檢查 `device_mapping.json` 中是否有 `DoMapAoGroups` 配置

### 2. 連線失敗
- 檢查遠端 IP 和 Port 是否正確
- 檢查網路連通性：`ping ***************`
- 檢查遠端 ModbusTCP Slave 是否運行

### 3. 輪詢不工作
- 檢查日誌中是否有異常訊息
- 使用手動輪詢 API 測試
- 檢查 DoMapAoGroups 配置是否正確

### 4. 智慧型重試不生效
- 檢查日誌中的 "Smart retry delay" 訊息
- 觀察連續失敗時的延遲時間是否遞增

## 📊 效能監控

### 連線狀態監控
定期呼叫狀態 API 監控連線健康度：
```bash
watch -n 5 'curl -s http://localhost:5000/api/do-to-ao/status | jq'
```

### 日誌分析
監控重試頻率和成功率：
```bash
tail -f logs/debug-*.log | grep -E "(Smart retry delay|Successfully connected|Failed to connect)"
```

這個測試指南可以幫助你驗證 DoToAo 功能的改善是否正常工作。
