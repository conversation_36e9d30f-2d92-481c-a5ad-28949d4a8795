﻿using System.Net.Sockets;
using System.Net;
using System.Text.Json;
using System.Text;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using System.Xml.Linq;
using TwentyFourDioGateway.DawuNursing.Model;
using TwentyFourDioGateway.Data;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway.DawuNursing
{
    public class RequestModelQueueItem
    {
        public RequestModelQueueItem(RequestModel model, DateTime expireTime)
        {
            Model = model;
            this.ExpireTime = expireTime;
        }

        public RequestModel Model { get; set; }
        public bool Expired => (DateTime.Now - ExpireTime).TotalMilliseconds > 0;
        public DateTime ExpireTime { get; set; }
    }
    public class DawuService
    {
        private  UdpClient? client;
        private UdpClient? responseListenerClient;
        private DawuSetting dawuSetting;
        private Setting setting;        
        private List<Task> tasks= new List<Task>();
        private Dictionary<int, UdpClient> responseListenerClients = new Dictionary<int, UdpClient>();
        //public ConcurrentQueue<RequestModelQueueItem> RequestQueue = new ConcurrentQueue<RequestModelQueueItem>();
        public ConcurrentQueue<RequestModel> RequestQueue = new ConcurrentQueue<RequestModel>();
        //Byte[] bytesEncode = System.Text.Encoding.UTF8.GetBytes(inputText); //取得 UTF8 2進位 Byte
        //string resultEncode = Convert.ToBase64String(bytesEncode); // 轉換 Base64 索引表
        private readonly IServiceProvider serviceProvider;
        private readonly JsonSerializerOptions sendOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
        private readonly ILogger<DawuService> logger;
        public DawuService(
            ILogger<DawuService> logger,
            IServiceProvider serviceProvider,
            IOptions<DawuSetting> options, IOptions<Setting> setting)
        {
            this.logger = logger;
            this.serviceProvider = serviceProvider;
            this.setting = setting.Value;
            dawuSetting = options.Value;
            if (setting.Value.EnablePingRongDawuNursing)
            {
                try
                {
                    foreach (var subnetChannel in dawuSetting.SubnetChannels)
                    {
                        responseListenerClients.Add(subnetChannel.SubnetChannelId,new UdpClient(new IPEndPoint(IPAddress.Parse(subnetChannel.SourceIp), subnetChannel.SourcePort)));
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"Time:{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss:fff")} :e");
                    responseListenerClients.Clear();
                    client = null;
                }
            }
        }
        //public async Task SendAsync(List<NodeModel> nodes)
        //{

        //}
        public async Task SendAsync(string nodeIp, byte nodeIndex, bool status)
        {
            if (setting.EnablePingRongDawuNursing)
            {
                var nodes = dawuSetting.Nodes
                     .Where(n => n.Ip == nodeIp)
                     .Where(n => n.Index == nodeIndex)
                     .ToList();
                if (nodes.Any())
                {
                    await SendAsync(nodes, status);
                }
            }
        }
        public async Task SendAsync(int manualConfirmAddress, bool status)
        {
            if (setting.EnablePingRongDawuNursing)
            {
                var nodes = dawuSetting.Nodes
                     .Where(n => n.ManualConfirmAddress == manualConfirmAddress)                     
                     .ToList();
                if (nodes.Any())
                {
                    await SendAsync(nodes, status);
                }
            }
        }
        public async Task SendAsync(List<NodeModel> nodes, bool status)        
        {            
            if (setting.EnablePingRongDawuNursing)
            {
                if (nodes.Any())
                {
                    try
                    {
                        var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
                        //var node = nodes.First();
                        Console.WriteLine($"{nodes.Count()} Nodes found.");
                        foreach (var node in nodes)
                        {
                            string jstring = string.Empty;
                            var channels = dawuSetting.SubnetChannels.Where(c => c.SubnetChannelId == node.SubnetChannelId).ToList();
                            var udps = responseListenerClients.Where(e => e.Key == node.SubnetChannelId).ToList();
                            if (udps.Any() && channels.Any())
                            {
                                var udp = udps.First().Value;
                                var channel = channels.First();
                                if (client == null)
                                {
                                    client = new UdpClient(new IPEndPoint(IPAddress.Parse(channel.SourceIp), channel.SourcePort));

                                }
                                if (node.Reversed)
                                {
                                    status = !status;
                                }
                                var model = new RequestModel()
                                {
                                    SipNumber = node.Id,
                                    NurseStation = node.NurseStation,
                                    DisplayName = Convert.ToBase64String(Encoding.UTF8.GetBytes(node.DisplayName)),
                                    Mac = channel.SourceMac,
                                    Ip = channel.SourceIp,
                                    CallingType = node.CallingType,
                                    Action = status ? (byte)1 : (byte)0,
                                    SubnetChannelId = node.SubnetChannelId
                                };
                                jstring = JsonSerializer.Serialize(model, sendOptions);
                                try
                                {                                    
                                    await db.AddAsync(new DawuHistory()
                                    {
                                        DateTime = DateTimeOffset.FromUnixTimeSeconds(model.TimeStamp-28800).DateTime.ToLocalTime(),
                                        Body = jstring,
                                        TwentyFourDioIndex = (byte)node.Index,
                                        TwentyFourDioIp = node.Ip,
                                        Status = status,
                                        SubnetChannelId = (byte)node.SubnetChannelId
                                    });
                                    await db.SaveChangesAsync();
                                }
                                catch (Exception e)
                                {
                                    logger.LogWarning(e, "Save dawu history failed");                                    
                                }
                       
                                byte[] bytes = Encoding.ASCII.GetBytes(jstring);


                                RequestQueue.Enqueue(model);
                                Console.WriteLine($"Time:{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss:fff")}: Request Queue Size: {RequestQueue.Count}, {node.DeviceIdRawString} : {node.DisplayName}");
                                Console.WriteLine($"Send Body: {jstring}");

                                try
                                {
                                    var sendTimeoutTask = Task.Delay(dawuSetting.SendTimeoutMilliseconds);
                                    var sendTask = udp.SendAsync(bytes, bytes.Length, IPEndPoint.Parse($"{channel.TargetIp}:{channel.TargetPort}"));
                                    var sendResult = await Task.WhenAny(new List<Task>() { sendTask, sendTimeoutTask });
                                    if (sendResult == sendTimeoutTask)
                                    {
                                        throw new Exception("Send Timeout");
                                    }
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);
                                }

                            }
                            else
                            {
                                Console.WriteLine($"Time:{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss:fff")}: No channel or connection Found, {node.DeviceIdRawString} : {node.DisplayName} {jstring}");
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        client = null;
                        
                    }
                    
                }
            }
     

        }
        //public async Task SendAsync(string deviceId, string nurseStation, bool status, string displayName)
        //{
        //    var jstring = JsonSerializer.Serialize(new RequestModel()
        //    {
        //        SipNumber = Convert.ToBase64String(Encoding.UTF8.GetBytes(deviceId)),
        //        NurseStation = nurseStation,
        //        DisplayName = Convert.ToBase64String(Encoding.UTF8.GetBytes(displayName)),
        //        Mac = sourceMac,
        //        Ip = sourceIp,
        //        Action = status ? (byte)1 : (byte)0,
        //    }, options);
        //    Console.WriteLine(jstring);
        //    byte[] bytes = Encoding.ASCII.GetBytes(jstring);
        //    await client.SendAsync(bytes, bytes.Length, IPEndPoint.Parse("***************"));
        //}
    }
}
