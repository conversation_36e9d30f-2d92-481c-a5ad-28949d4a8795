﻿{
  "DeviceMapping": {
    "BindDoWithDiAddresses": [
      50
    ],
    "Devices": [
      {
        "Ip": "************",
        "Port": 5801,
        "DiStartAddress": 10001,
        "DoStartAddress": 10001
      }
    ],
    "BaUrl": "http://*************",
    "PublicVideoUrlPrefix": "http://office.weema.com.tw:56790",
    "RecordPath": "C:\\Weema\\records",
    "RecordTimeoutSeconds": 300,
    "HundureServerAddress": "***************:88",
    "HiSharpDefaultUsername": "admin",
    "HiSharpDefaultPassword": "123456",
    "HiSharpRecordWaitSeconds": 60,
    "HiSharpUsePlaybackMode": false,
    "HiSharpUseVlcRecordingMode": false,
    "FuhoMotionDetectionAlarmTimeoutSeconds": 30,
    "HiSharpAiCameraAlarmTimeoutSeconds": 30,
    "FuhoDefaultUsername": "admin",
    "FuhoDefaultPassword": "admin",
    "PingTimeout": 1000,
    "PingBatchSize": 200,
    "PingRetryAttempts": 1,
    "PingDeadMinutes": 5,
    "HiSharpAiCameras": [

    ],
    "HundureCardReaderEndpoints": [
      {
        "DoorName": "94MSKL",
        "IllegalCardModbusAddress": 50
      }
    ],
    "PingIps": [
      {
        "StartAddress": "***********",
        "Length": 254,
        "ModbusAddress": 1001
      },
      {
        "StartAddress": "***********",
        "Length": 254,
        "ModbusAddress": 1301
      },
      {
        "StartAddress": "*************",
        "Length": 254,
        "ModbusAddress": 1601
      }


    ],
    "FuhoMotionDetectionCameras": [
      {
        "Id": "*************",
        "ModbusAddress": 3000
      }
    ],
    "VideoCameras": [
      {
        "Brand": "HiSharp",
        "Id": 1,
        "Ip": "************"
      },
      {
        "Brand": "Fuho",
        "Id": 2,
        "Ip": "*************"
      },
      {
        "Brand": "HiSharp",
        "Id": 2000,
        "Ip": "************",
        "Port": 554
      }
    ],
    "AsteriskExtensions": [
      {
        "StartExtensionNumber": "1001",
        "EndExtensionNumber": "1100",
        "StartOnlineStatusModbusAddress": 4001,
        "StartCallStatusModbusAddress": 4101
      },
      {
        "StartExtensionNumber": "8811",
        "EndExtensionNumber": "8815",
        "StartOnlineStatusModbusAddress": 5001,
        "StartCallStatusModbusAddress": 5101
      },
      {
        "Extension": "888049",
        "OnlineStatusModbusAddress": 5006,
        "CallStatusModbusAddress": 5106
      },
      {
        "StartExtensionNumber": "9001",
        "EndExtensionNumber": "9020",
        "StartOnlineStatusModbusAddress": 5010,
        "StartCallStatusModbusAddress": 5110
      }
    ],
    "CwbWeatherApiKey": "CWA-3E744CED-C2A4-4194-93E5-12FB800B38F1",
    "CwbWeatherLocation": "臺北市",
    "CwbWeatherLocationName": "大安森林",
    "CwbWeatherUpdateIntervalMinutes": 1,
    "DoMapAoGroups": [
      {
        "GroupName": "測試群組1",
        "RemoteIp": "**************",
        "RemotePort": 502,
        "RemoteSlaveId": 1,
        "MaxPollingQuantity": 50,
        "Mappings": [
          {
            "LocalDoAddress": 900,
            "RemoteHoldingRegisterAddress": 1,
            "ValueWhenTrue": 255,
            "ValueWhenFalse": 0
          },
          {
            "LocalDoAddress": 901,
            "RemoteHoldingRegisterAddress": 2,
            "ValueWhenTrue": 255,
            "ValueWhenFalse": 0
          }
        ]
      }
    ],
    "KingmanAirGroups": [
      {
        "GroupName": "空調群組1",
        "RemoteIp": "*************",
        "RemotePort": 502,
        "Mappings": [
          {
            "AirConditionerId": 1,
            "DiAddress": 6001,
            "DoAddress": 6001
          },
          {
            "AirConditionerId": 2,
            "DiAddress": 6002,
            "DoAddress": 6002
          },
          {
            "AirConditionerId": 10,
            "DiAddress": 6010,
            "DoAddress": 6010
          }
        ]
      }
    ],
    "ModbusTcpProxyGroups": [
      {
        "GroupName": "測試Proxy群組1",
        "RemoteIp": "**************",
        "RemotePort": 502,
        "RemoteSlaveId": 1,
        "PollingIntervalMs": 2000,
        "CommandIntervalMs": 150,
        "MaxPollingQuantity": 30,
        "ConnectionTimeoutMs": 5000,
        "ReadTimeoutMs": 3000,
        "WriteTimeoutMs": 3000,
        "Enabled": true,
        "Ranges": [
          {
            "RangeName": "遠端Coils區段1",
            "DataType": 1,
            "RemoteStartAddress": 1,
            "RemoteEndAddress": 50,
            "LocalStartAddress": 7001,
            "Enabled": true
          },
          {
            "DataType": 2,
            "RemoteStartAddress": 1,
            "RemoteEndAddress": 50,
            "LocalStartAddress": 8001
          },
          {
            "RangeName": "",
            "DataType": 3,
            "RemoteStartAddress": 1,
            "RemoteEndAddress": 20,
            "LocalStartAddress": 9001,
            "Enabled": true
          },
          {
            "DataType": 4,
            "RemoteStartAddress": 1,
            "RemoteEndAddress": 20,
            "LocalStartAddress": 9501,
            "Enabled": false
          }
        ]
      }
    ]
  }
}