﻿# 腳本：從分機號碼清單產生裝置對應 JSON
# 功能：此腳本會讀取、排序並分組連續的分機號碼，然後為每個區塊指派 Modbus 位址，
#       並在區塊之間保留指定的間隔以供未來擴充。

<#
.SYNOPSIS
    從分機號碼檔案產生裝置對應 JSON。

.DESCRIPTION
    此腳本會從一個文字檔案讀取分機號碼，進行排序並移除重複項，然後將連續的號碼分組成範圍，
    並建立一個 JSON 設定檔。每個範圍都會被指派一個「上線狀態」和「通話狀態」的 Modbus 起始位址。
    「上線狀態」和「通話狀態」的位址空間是獨立的，您可以為它們分別設定起始點。
    每個區塊之間會保留一個位址緩衝區，以便未來新增裝置。

    腳本假設：
    - 輸入檔案可包含多個以空格或 Tab 分隔的號碼。
    - 以 '#' 開頭的行為註解，將被忽略。
    - 對於一個包含 N 個分機的區塊，「上線狀態」位址會佔用 N 個位置，「通話狀態」位址也會佔用 N 個位置，但它們位於不同的位址範圍。

.PARAMETER InputFile
    包含分機號碼清單的來源檔案路徑。

.PARAMETER OutputFile
    產生的 JSON 輸出檔案路徑。

.PARAMETER InitialOnlineStatusAddress
    第一個區塊「上線狀態」的起始 Modbus 位址。

.PARAMETER InitialCallStatusAddress
    第一個區塊「通話狀態」的起始 Modbus 位址。

.PARAMETER AddressGap
    在連續的裝置區塊之間保留的空白位址數量。

.EXAMPLE
    .\generate_mapping.ps1 -InputFile "extensions.txt" -OutputFile "generated_device_mapping.json" -InitialOnlineStatusAddress 4001 -InitialCallStatusAddress 5001 -AddressGap 5
#>
param (
    [string]$InputFile = "test.txt",
    [string]$OutputFile = "generated_device_mapping.json",
    [int]$InitialOnlineStatusAddress = 4001,
    [int]$InitialCallStatusAddress = 14001,
    [int]$AddressGap = 1
)

# 腳本職責：
# 這個腳本的目的是自動化從一個可能未經整理的分機號碼清單，產生一個結構化的設備映射設定檔。
# 它整合了號碼的解析、排序、去重、分組，並為每個組分配 Modbus 位址，同時在組之間保留擴展空間。
# 上線與通話狀態的位址是獨立管理的。

# 方法功能：
# - 讀取並解析輸入檔案，支援每行單一或多個號碼（以空格或 Tab 分隔）。
# - 過濾註解和空行。
# - 將所有號碼排序並移除重複項。
# - 識別連續的數字範圍（區塊）。
# - 為每個範圍計算 Modbus 位址。每個分機佔用 2 個位址（上線 + 通話）。
# - 生成 JSON 格式的輸出。
# - 輸入：一個純文字檔，包含分機號碼。
# - 輸出：一個 JSON 檔案，包含設備對應關係。

# 架構決策：
# 選擇 PowerShell 是因為它在 Windows 環境中普遍可用，並且能輕鬆處理檔案 I/O 和 JSON 操作。
# 將位址起始點和間隙設計為參數，增加了腳本的靈活性和可重用性。
# 將排序和解析功能內建，簡化了使用者的操作流程，只需要一個步驟就能完成所有處理。

# 效能考量：
# 對於非常大的輸入檔案（數百萬行），一次性讀取所有內容到記憶體中可能會消耗大量記憶體。
# 目前的實作對於一般大小的設定檔是高效的。
# 如果檔案變得非常大，可以考慮改用流式處理來優化記憶體使用。

try {
    Write-Host "開始處理檔案: $InputFile" -ForegroundColor Green

    # 檢查輸入檔案是否存在
    if (-not (Test-Path $InputFile)) {
        Write-Error "找不到輸入檔案: $InputFile"
        exit 1
    }

    # 讀取檔案，過濾掉註解和空行。
    $lines = Get-Content -Path $InputFile -ErrorAction Stop |
               Where-Object { $_ -notmatch '^\s*#' -and $_.Trim() -ne '' }
    
    Write-Host "正在從 $($lines.Count) 行資料中解析數字..." -ForegroundColor Yellow

    # 從行中解析所有數字，然後進行排序和去重
    $numbers = $lines -split '\s+' |
               Where-Object { $_ -match '^\d+$' } |
               ForEach-Object { [int]$_ } |
               Sort-Object -Unique

    # 檢查是否有數字需要處理
    if ($null -eq $numbers -or $numbers.Count -eq 0) {
        Write-Warning "輸入檔案 '$InputFile' 中不包含任何有效的數字可供處理。"
        return
    }

    Write-Host "總共找到 $($numbers.Count) 個有效且唯一的數字。" -ForegroundColor Cyan
    Write-Host "正在將數字分組並計算 Modbus 位址..." -ForegroundColor Yellow

    $mappings = [System.Collections.Generic.List[PSCustomObject]]::new()
    $nextOnlineStatusAddress = $InitialOnlineStatusAddress
    $nextCallStatusAddress = $InitialCallStatusAddress

    $startIndex = 0
    # 遍歷數字以識別區塊
    for ($i = 1; $i -lt $numbers.Count; $i++) {
        # 如果當前數字不是前一個數字加一，表示一個區塊的結束
        if ($numbers[$i] -ne ($numbers[$i - 1] + 1)) {
            $startExtension = $numbers[$startIndex]
            $endExtension = $numbers[$i - 1]
            $rangeSize = $endExtension - $startExtension + 1

            $block = [PSCustomObject]@{
                StartExtensionNumber           = "$startExtension"
                EndExtensionNumber             = "$endExtension"
                StartOnlineStatusModbusAddress = $nextOnlineStatusAddress
                StartCallStatusModbusAddress   = $nextCallStatusAddress
            }
            $mappings.Add($block)

            # 更新下一個區塊的起始位址。
            # Online Status 和 Call Status 的位址各自獨立增加。
            # 位移量 = 當前區塊的大小 + 區塊間的間隙。
            $nextOnlineStatusAddress += $rangeSize + $AddressGap
            $nextCallStatusAddress += $rangeSize + $AddressGap
            # 設定下一個區塊的起始索引
            $startIndex = $i
        }
    }

    # 處理最後一個區塊
    if ($startIndex -lt $numbers.Count) {
        $startExtension = $numbers[$startIndex]
        $endExtension = $numbers[-1]
        
        $block = [PSCustomObject]@{
            StartExtensionNumber           = "$startExtension"
            EndExtensionNumber             = "$endExtension"
            StartOnlineStatusModbusAddress = $nextOnlineStatusAddress
            StartCallStatusModbusAddress   = $nextCallStatusAddress
        }
        $mappings.Add($block)
    }

    Write-Host "正在產生 JSON 檔案: $OutputFile" -ForegroundColor Yellow

    # 將結果轉換為JSON格式並儲存到檔案
    # 使用 -Depth 5 以確保物件正確序列化
    # 使用 'utf8' 編碼以確保相容性，在多數 Windows 環境下它會包含 BOM
    $mappings | ConvertTo-Json -Depth 5 | Out-File -FilePath $OutputFile -Encoding utf8 -ErrorAction Stop

    Write-Host "成功產生裝置對應檔案: '$OutputFile'" -ForegroundColor Green

}
catch {
    Write-Error "發生錯誤: $_"
    # Exit with a non-zero status code to indicate failure
    exit 1
} 