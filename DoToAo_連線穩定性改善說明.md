# DoToAo 連線穩定性改善說明

**日期：2024-12-19**

## 問題描述

DoToAo 功能有時候會跑一跑就不動了，主要原因是連線異常（包含異常回應、回應 TIMEOUT 等等）時沒有有效的重試和重連機制。

## 改善策略

實作了一個**智慧型重試機制**，既能快速恢復服務，又不會造成 socket 資源浪費：

### 1. 快速重試 + 漸進式延遲

- **第一次失敗**：立即重試（0ms 延遲）
- **連續失敗**：使用漸進式延遲策略
  - 第2次失敗：100ms 延遲
  - 第3次失敗：300ms 延遲
  - 第4次失敗：500ms 延遲
  - 第5次失敗：1000ms 延遲
  - 第6次以上失敗：2000ms 延遲（上限）

### 2. 強化連線狀態檢測

- 使用 `Socket.Poll()` 方法檢查真實連線狀態
- 不只依賴 `TcpClient.Connected` 屬性
- 能更準確檢測到斷線情況

### 3. Timeout 處理機制

- 為所有網路 I/O 操作添加 5 秒 timeout
- 使用 `CancellationToken` 控制超時
- 分階段讀取避免部分讀取問題

## 主要修改內容

### ModbusTcpMasterClient.cs

1. **新增智慧型重試欄位**：
   ```csharp
   private int _consecutiveFailures = 0;
   private DateTime _lastFailureTime = DateTime.MinValue;
   private readonly int[] _retryDelaysMs = { 0, 100, 300, 500, 1000, 2000 };
   private DateTime _lastSuccessTime = DateTime.MinValue;
   ```

2. **強化連線狀態檢查**：
   ```csharp
   public bool IsConnected
   {
       get
       {
           if (_tcpClient?.Connected != true || _networkStream == null)
               return false;
           
           try
           {
               return _tcpClient.Client.Poll(0, SelectMode.SelectRead) ? 
                   _tcpClient.Client.Available > 0 : true;
           }
           catch
           {
               return false;
           }
       }
   }
   ```

3. **添加 Timeout 處理**：
   - 讀取和寫入操作都使用 5 秒 timeout
   - 使用 `ReadExactBytesAsync` 確保完整讀取
   - 分別處理 `OperationCanceledException` (timeout) 和其他異常

4. **智慧型重試邏輯**：
   - `RecordSuccess()`: 成功時重設失敗計數器
   - `RecordFailure()`: 失敗時增加失敗計數器
   - `GetSmartRetryDelay()`: 根據失敗次數計算延遲時間
   - `SmartDelayAsync()`: 執行智慧型延遲

### DoToAoService.cs

1. **簡化錯誤處理**：
   - 移除手動清理客戶端連線的邏輯
   - 讓 `ModbusTcpMasterClient` 的內建重試機制處理連線問題
   - 減少不必要的 socket 資源浪費

2. **保留連線狀態檢查**：
   - 在 `GetOrCreateMasterClient()` 中仍然檢查連線狀態
   - 只在確實需要時才重新創建客戶端

## 效果預期

### 快速恢復
- 第一次失敗立即重試，大部分暫時性問題能快速恢復
- 減少服務中斷時間

### 避免資源浪費
- 漸進式延遲避免過度重試
- 不會快速消耗 socket 資源
- 智慧型連線狀態檢測減少不必要的重連

### 穩定性提升
- Timeout 機制防止無限等待
- 強化的異常處理確保服務持續運行
- 詳細的日誌記錄便於問題診斷

## 使用建議

1. **監控日誌**：觀察重試延遲和連線狀態日誌
2. **調整參數**：如需要可調整 `_retryDelaysMs` 陣列中的延遲時間
3. **網路環境**：在不穩定的網路環境中，此機制能顯著提升穩定性

## 技術細節

- **執行緒安全**：使用 `SemaphoreSlim` 確保連線操作的執行緒安全
- **記憶體效率**：重用連線物件，避免頻繁創建和銷毀
- **效能優化**：智慧型讀取範圍合併，減少網路請求次數
- **錯誤隔離**：單一群組的問題不會影響其他群組的運作

這個改善方案在保持快速回應的同時，有效防止了資源浪費和系統過載問題。
