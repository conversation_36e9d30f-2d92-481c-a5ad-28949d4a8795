﻿
using Microsoft.EntityFrameworkCore;
using TwentyFourDioGateway.Data;

namespace TwentyFourDioGateway.SafetyMsg
{
    public class NotificationBackgroundService : BackgroundService
    {
        private readonly IServiceProvider serviceProvider;
        public NotificationBackgroundService(IServiceProvider serviceProvider)
        {
            this.serviceProvider = serviceProvider;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {

                try
                {
                    var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
                    var request = await db.RecordingRequests
                        .Where(r => r.SendNotification)
                        .Where(r => r.IsSuccessed == true)
                        .Where(r => !r.IsNotificationCompleted)
                        .OrderBy(r => r.Time)
                        .FirstOrDefaultAsync();
                    if (request != null)
                    {

                        var notificationService = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<NotificationService>();
                        var smsReceivers = request.SmsReceivers.Trim().Split(",");
                        var emailReceivers = request.EmailReceivers.Split(",");
                        if (smsReceivers.Any())
                        {
                            foreach (var smsReceiver in smsReceivers.Where(s => !string.IsNullOrWhiteSpace(s)))
                            {
                                await notificationService.SendSmsAsync(smsReceiver, request.DisplayName , request.Time, request.PublicVideoUrl, stoppingToken);                                
                            }
                        }
                        if (emailReceivers.Any())
                        {
                            foreach (var emailReceiver in emailReceivers.Where(s => !string.IsNullOrWhiteSpace(s)))
                            {
                                await notificationService.SendMailAsync(emailReceiver, request.DisplayName, request.Time, request.PublicVideoUrl, stoppingToken);
                                
                            }
                        }
                        request.IsNotificationCompleted = true;
                        request.IsNotificationSuccessed = true;
                        await db.SaveChangesAsync();
                        await Task.Delay(100, stoppingToken);
                    }
                    else
                    {
                        await Task.Delay(1000, stoppingToken);
                    }

                }
                catch (Exception e)
                {

                }
            }
        }
    }
}
