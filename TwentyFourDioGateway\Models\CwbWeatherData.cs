namespace TwentyFourDioGateway.Models
{
    public class CwbWeatherConfig
    {
        public required string ApiKey { get; set; }
        public required string Location { get; set; }
        public required string LocationName { get; set; }
        public int UpdateIntervalMinutes { get; set; }
    }

    public class CwbWeatherData
    {
        public DateTime ObservationTime { get; set; }
        public double Temperature { get; set; }
        public double RelativeHumidity { get; set; }
        public required string WeatherDescription { get; set; }
        public double RainfallMM { get; set; }
        public double WindSpeed { get; set; }
        public required string WindDirection { get; set; }
    }
}