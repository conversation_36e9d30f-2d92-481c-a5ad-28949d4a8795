﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Modbus.Data;
using SQLitePCL;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Net;
using System.Net.NetworkInformation;
using System.Xml.Linq;
using TwentyFourDioGateway.Data;
using TwentyFourDioGateway.DawuNursing;
using TwentyFourDioGateway.DawuNursing.Model;
using TwentyFourDioGateway.Models;
using TwentyFourDioGateway.SafetyMsg;

namespace TwentyFourDioGateway
{
    public class CommandItem
    {
        public string Id { get; set; } = string.Empty;
        public byte Index { get; set; } = 1;
        public bool Value { get; set; } = false;
    }


    public class GatewayService
    {
        private List<Task> Tasks { get; set; } = new List<Task>();
        public DataStore dataStore = DataStoreFactory.CreateDefaultDataStore(ushort.MaxValue, ushort.MaxValue, ushort.MaxValue, 0);
        private Setting Setting { get; set; } = new Setting();
        private List<TwentyFourDioDevice> twentyFourDioDevices = new List<TwentyFourDioDevice>();
        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
        //command to 24dio
        public ConcurrentQueue<CommandItem> commandQueue = new ConcurrentQueue<CommandItem>();
        private readonly DawuService dawuService;
        private readonly DawuSetting dawuSetting;
        //private readonly AppDbContext db;
        private readonly ILogger<GatewayService> logger;
        private readonly IServiceProvider serviceProvider;
        private readonly DeviceMapping deviceMapping;
        private readonly VideoRecordingService hiSharpVideoRecordingService;
        private readonly NotificationService notificationService;
        private readonly DoToAoService? doToAoService;
        private readonly KingmanAirService? kingmanAirService;
        public GatewayService(
            VideoRecordingService hiSharpVideoRecordingService,
            IServiceProvider serviceProvider,
            ILogger<GatewayService> logger,
            IOptions<Setting> options, IOptions<DawuSetting> dawuSetting, IOptions<DeviceMapping> deviceMapping, DawuService dawuService, NotificationService notificationService, DoToAoService? doToAoService = null, KingmanAirService? kingmanAirService = null)
        {
            this.hiSharpVideoRecordingService = hiSharpVideoRecordingService;
            this.serviceProvider = serviceProvider;
            this.logger = logger;
            this.dawuService = dawuService;
            this.notificationService = notificationService;
            this.doToAoService = doToAoService;
            this.kingmanAirService = kingmanAirService;

            Setting = options.Value;
            if (Setting.EnablePingRongDawuNursing)
            {
                this.dawuSetting = dawuSetting.Value;
            }
            twentyFourDioDevices = deviceMapping.Value.Devices;
            this.deviceMapping = deviceMapping.Value;

            // 訂閱DO狀態變更事件（如果服務已啟用）
            if (this.doToAoService != null)
            {
                this.doToAoService.DoStateChanged += OnDoStateChanged;
            }

            // 訂閱空調DI狀態變更事件（如果服務已啟用）
            if (this.kingmanAirService != null)
            {
                this.kingmanAirService.DiStateChanged += OnDiStateChanged;
                this.kingmanAirService.DoStateChanged += OnDoStateChangedFromAir;
            }
        }

        /// <summary>
        /// 處理DO狀態變更事件（由輪詢遠端Holding Register觸發）
        /// </summary>
        /// <param name="doAddress">DO位址</param>
        /// <param name="newState">新的DO狀態</param>
        private void OnDoStateChanged(ushort doAddress, bool newState)
        {
            try
            {
                // 更新ModbusTCP Slave的DO狀態
                dataStore.CoilDiscretes[doAddress] = newState;

                logger.LogInformation("DO state updated by remote polling: Address={DoAddress}, State={NewState}", doAddress, newState);

                // 如果有綁定的DI位址，也一併更新
                if (deviceMapping.BindDoWithDiAddresses.Exists(a => a == doAddress))
                {
                    dataStore.InputDiscretes[doAddress] = newState;
                    logger.LogDebug("Updated bound DI address {Address} to {Value}", doAddress, newState);
                }

                // 非同步更新資料庫狀態
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await UpdateSavedStatus(ModbusAddressType.Coil, doAddress, newState);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Exception occurred while updating DO state to database: Address={DoAddress}", doAddress);
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception occurred while processing DO state change event: Address={DoAddress}, State={NewState}", doAddress, newState);
            }
        }

        /// <summary>
        /// 處理DI狀態變更事件（由輪詢空調狀態觸發）
        /// </summary>
        /// <param name="diAddress">DI位址</param>
        /// <param name="newState">新的DI狀態</param>
        private void OnDiStateChanged(ushort diAddress, bool newState)
        {
            try
            {
                // 更新ModbusTCP Slave的DI狀態
                dataStore.InputDiscretes[diAddress] = newState;

                logger.LogInformation("DI state updated by air conditioner polling: Address={DiAddress}, State={NewState}", diAddress, newState);

                // 非同步更新資料庫狀態
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await UpdateSavedStatus(ModbusAddressType.DiscreteInput, diAddress, newState);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Exception occurred while updating DI state to database: Address={DiAddress}", diAddress);
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception occurred while processing DI state change event: Address={DiAddress}, State={NewState}", diAddress, newState);
            }
        }

        /// <summary>
        /// 處理DO狀態變更事件（由空調DI狀態變更觸發，用於同步DO狀態）
        /// </summary>
        /// <param name="doAddress">DO位址</param>
        /// <param name="newState">新的DO狀態</param>
        private void OnDoStateChangedFromAir(ushort doAddress, bool newState)
        {
            try
            {
                // 更新ModbusTCP Slave的DO狀態，使其與DI狀態同步
                dataStore.CoilDiscretes[doAddress] = newState;

                logger.LogInformation("DO state synchronized with air conditioner DI state: Address={DoAddress}, State={NewState}", doAddress, newState);

                // 非同步更新資料庫狀態
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await UpdateSavedStatus(ModbusAddressType.Coil, doAddress, newState);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Exception occurred while updating DO state to database: Address={DoAddress}", doAddress);
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception occurred while processing DO state change event from air conditioner: Address={DoAddress}, State={NewState}", doAddress, newState);
            }
        }

        private async Task UpdateSavedStatus(ModbusAddressType modbusAddressType, ushort address, bool status)
        {
            await _semaphore.WaitAsync();
            try
            {
                var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
                var dbNode = await db.Nodes.Where(n => n.Address == address)
                  .Where(n => n.AddressType == modbusAddressType)
                  .FirstOrDefaultAsync();
                if (dbNode == null)
                {
                    db.Nodes.Add(new Node()
                    {
                        Address = (ushort)address,
                        Value = status,
                        AddressType = modbusAddressType,
                        UpdateTime = DateTime.Now
                    });
                    await db.SaveChangesAsync();
                }
                else
                {
                    dbNode.Value = status;
                    dbNode.UpdateTime = DateTime.Now;
                    await db.SaveChangesAsync();
                }

            }
            catch (Exception exp)
            {
                logger.LogError(exp, "Error when db UpdateStatus");
            }
            finally
            {
                _semaphore.Release();
            }
        }
        private void UpdateModbusStatus(ModbusAddressType modbusAddressType, ushort address, bool status)
        {
            if (modbusAddressType == ModbusAddressType.Coil)
            {
                dataStore.CoilDiscretes[address] = status;


            }
            else if (modbusAddressType == ModbusAddressType.DiscreteInput)
            {
                dataStore.InputDiscretes[address] = status;
            }
        }

        // get status from db
        public async Task<Node?> GetDbStatusAsync(ModbusAddressType modbusAddressType, ushort address)
        {
            var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
            var dbNode = await db.Nodes.Where(n => n.Address == address)
                .Where(n => n.AddressType == modbusAddressType)
                .FirstOrDefaultAsync();
            if (dbNode != null)
            {
                return dbNode;
            }

            return null;
        }
        public void EnqueueWriteDoToTwentyFourDio(ushort address, bool status)
        {
            var device = twentyFourDioDevices
                             .Where(t => t.DoStartAddress <= address)
                             .Where(t => t.DoStartAddress + 8 > address)
                             .FirstOrDefault();
            if (device != null)
            {
                commandQueue.Enqueue(new CommandItem()
                {
                    Id = device.Id,
                    Index = (byte)(address - device.DoStartAddress),
                    Value = status
                });
            }
        }
        public async Task UpdateStatusAsync(ModbusAddressType modbusAddressType,ushort address, bool status)
        {
            if (deviceMapping.BindDoWithDiAddresses.Contains(address))
            {
                if (modbusAddressType == ModbusAddressType.Coil)
                {
                    UpdateModbusStatus(ModbusAddressType.DiscreteInput, address, status);
                    await UpdateSavedStatus(ModbusAddressType.DiscreteInput, address, status);
                }
                else if (modbusAddressType == ModbusAddressType.DiscreteInput)
                {
                    UpdateModbusStatus(ModbusAddressType.Coil, address, status);
                    await UpdateSavedStatus(ModbusAddressType.Coil, address, status);
                }
            }

            UpdateModbusStatus(modbusAddressType, address, status);
            await UpdateSavedStatus(modbusAddressType,address,status);
        }

        public async Task RestoreModbusDataAsync()
        {
            var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
            var dbNodes = await db.Nodes.ToListAsync();
            foreach (var dbNode in dbNodes)
            {
                if (dbNode.AddressType == Models.ModbusAddressType.Coil)
                {
                    dataStore.CoilDiscretes[dbNode.Address] = dbNode.Value;
                    if (deviceMapping.BindDoWithDiAddresses.Exists(e => e == dbNode.Address))
                    {
                        dataStore.InputDiscretes[dbNode.Address] = dbNode.Value;
                    }
                }
                else if (dbNode.AddressType == Models.ModbusAddressType.DiscreteInput)
                {
                    dataStore.InputDiscretes[dbNode.Address] = dbNode.Value;
                }
            }
            logger.LogInformation($"RestoreModbusDataAsync ok { dbNodes.Count() } nodes restores");
        }
        // 用於同步 ModbusTCP 請求處理的信號量
        private readonly SemaphoreSlim _modbusRequestSemaphore = new SemaphoreSlim(1, 1);

        /// <summary>
        /// 處理 ModbusTCP Slave 值變更事件
        /// </summary>
        public void ModbusServer_valuesChanged(object? sender, Modbus.Device.ModbusSlaveRequestEventArgs e)
        {
            try
            {
                if (e.Message.MessageFrame[1] == 0x05) // write single coil
                {
                    var address = e.Message.MessageFrame[2] * 256 + e.Message.MessageFrame[3] + 1;
                    var val = e.Message.MessageFrame[4] == 0xff;

                    logger.LogInformation("ModbusTCP Slave 收到寫入單一線圈請求: 位址={Address}, 值={Value}", address, val);

                    // 使用 Task.Run 非同步處理，但使用信號量確保同步
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // 使用信號量限制同時處理的請求數量
                            await _modbusRequestSemaphore.WaitAsync();

                            try
                            {
                                // 檢查是否為空調控制DO位址
                                bool isAirConditionerDo = kingmanAirService?.IsAirConditionerDoAddress((ushort)address) == true;

                                if (isAirConditionerDo)
                                {
                                    // 空調控制DO，需要恢復到寫入前的原始狀態
                                    // 因為ModbusTCP已經自動更新了狀態，我們需要恢復到原來的狀態
                                    // 這裡我們需要從資料庫或其他地方取得真正的原始狀態

                                    // 先嘗試從資料庫取得原始狀態
                                    bool originalState = false;
                                    try
                                    {
                                        using var scope = serviceProvider.CreateScope();
                                        using var db = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                                        var node = await db.Nodes.FirstOrDefaultAsync(n => n.Address == address && n.AddressType == ModbusAddressType.Coil);
                                        if (node != null)
                                        {
                                            originalState = node.Value;
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogWarning(ex, "Failed to get original state from database for address {Address}, using false as default", address);
                                    }

                                    // 恢復原始狀態
                                    dataStore.CoilDiscretes[address] = originalState;

                                    logger.LogInformation("Air conditioner DO write received: Address={Address}, Value={Value}, OriginalState={OriginalState} restored, will be updated by DI polling", address, val, originalState);
                                }
                                else
                                {
                                    // 非空調控制DO，正常更新 SQLite 狀態
                                    await UpdateStatusAsync(ModbusAddressType.Coil, (ushort)address, val);
                                }

                                // 處理大武護理站功能
                                if (Setting.EnablePingRongDawuNursing)
                                {
                                    await dawuService.SendAsync(address, val);
                                }

                                // 處理視頻錄製功能
                                if (Setting.EnableVideoRecording && val)
                                {
                                    try
                                    {
                                        await hiSharpVideoRecordingService.channel.Writer.WriteAsync(new RecordingRequest()
                                        {
                                            VideoCamId = address,
                                            Time = DateTime.Now,
                                        });
                                        logger.LogInformation("已將攝影機 ID={CameraId} 加入錄影佇列", address);
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError(ex, "處理視頻錄製請求時發生異常: 位址={Address}", address);
                                    }
                                }

                                // 處理 DO 寫入到 24DIO 裝置
                                EnqueueWriteDoToTwentyFourDio((ushort)address, val);

                                // 處理 DO轉AO 功能（如果服務已啟用）
                                if (doToAoService != null)
                                {
                                    try
                                    {
                                        await doToAoService.ProcessDoChangeAsync((ushort)address, val);
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError(ex, "處理DO轉AO時發生異常: 位址={Address}, 值={Value}", address, val);
                                    }
                                }

                                // 處理空調控制功能（如果服務已啟用）
                                if (kingmanAirService != null)
                                {
                                    try
                                    {
                                        await kingmanAirService.ProcessDoChangeAsync((ushort)address, val);
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError(ex, "處理空調控制時發生異常: 位址={Address}, 值={Value}", address, val);
                                    }
                                }

                                // 處理綁定的 DI 位址
                                if (deviceMapping.BindDoWithDiAddresses.Exists(a => a == address))
                                {
                                    dataStore.InputDiscretes[address] = val;
                                    logger.LogDebug("已更新綁定的 DI 位址 {Address} 為 {Value}", address, val);
                                }
                            }
                            finally
                            {
                                // 釋放信號量
                                _modbusRequestSemaphore.Release();
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogError(ex, "處理 ModbusTCP 寫入請求時發生異常: 位址={Address}, 值={Value}", address, val);
                        }
                    });
                }
                else if (e.Message.MessageFrame[1] == 15) // 寫入多個線圈
                {
                    logger.LogWarning("收到多線圈寫入請求 (功能碼 15)，目前不支援此功能");
                }
                else
                {
                    logger.LogDebug("收到 ModbusTCP 請求: 功能碼={FunctionCode}", e.Message.MessageFrame[1]);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "處理 ModbusTCP 請求時發生異常");
            }

        }
        public void UpdateOrtisElevatorStatus(TwentyFourDioDevice device, IEnumerable<bool> values)
        {
            try
            {
                if (values.Count() == 24)
                {
                    var ortis1 = values.Skip(device.OrtisElevatorStartBit1 - 1).Take(6).Reverse().ToList();
                    var ortis2 = values.Skip(device.OrtisElevatorStartBit2 - 1).Take(6).Reverse().ToList();
                    dataStore.HoldingRegisters[device.OrtisElevatorMapAddress1] = (ushort)ortis1.Aggregate(0, (acc, e) => acc * 2 + (e ? 1 : 0));
                    dataStore.HoldingRegisters[device.OrtisElevatorMapAddress2] = (ushort)ortis2.Aggregate(0, (acc, e) => acc * 2 + (e ? 1 : 0));
                    logger.LogInformation($"Ortis Elevator {device.Ip}@{device.Port}: 1: {ortis1.Select(o => o ? "1" : "0").Aggregate((p, n) => $"{p}{n}")},2: {ortis2.Select(o => o ? "1" : "0").Aggregate((p, n) => $"{p}{n}")}");
                    logger.LogInformation($"Ortis Elevator {device.Ip}@{device.Port}: 1h: {dataStore.HoldingRegisters[device.OrtisElevatorMapAddress1]}, 2h: {dataStore.HoldingRegisters[device.OrtisElevatorMapAddress2]}");

                }
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error when UpdateOrtisElevatorStatus");
            }
        }
        /// <summary>
        /// 更新數位輸入狀態
        /// </summary>
        /// <param name="device">24DIO裝置</param>
        /// <param name="values">數位輸入值</param>
        public async Task UpdateDiStatus(TwentyFourDioDevice device, IEnumerable<bool> values)
        {
            try
            {
                if (values.Count() == 24)
                {
                    for (int i = 0; i < 24; i++)
                    {
                        var newValue = values.Skip(i).First();
                        var oldValue = dataStore.InputDiscretes[i + device.DiStartAddress];

                        if (oldValue != newValue)
                        {
                            logger.LogInformation($"Di Modbus: {i+device.DiStartAddress} / 24dio: {device.Ip}@{i+1} Change to: {newValue}");
                            await UpdateStatusAsync(ModbusAddressType.DiscreteInput, (ushort)(i + device.DiStartAddress), newValue);
                            if (Setting.EnablePingRongDawuNursing)
                            {
                                await dawuService.SendAsync(device.Ip, (byte)(i + 1), values.Skip(i).First());
                            }

                            // 當DI狀態變為true且啟用了電子郵件通知功能時發送通知
                            if (newValue && Setting.EnableDiEmailNotification && Setting.DiEmailRecipients?.Any() == true)
                            {
                                // 使用模板生成郵件內容，替換變數
                                string emailBody = Setting.DiEmailBodyTemplate
                                    .Replace("{DeviceId}", device.Id ?? "未知")
                                    .Replace("{DeviceIp}", device.Ip)
                                    .Replace("{DiNumber}", (i + 1).ToString())
                                    .Replace("{OldStatus}", oldValue.ToString().ToLower())
                                    .Replace("{NewStatus}", newValue.ToString().ToLower())
                                    .Replace("{DateTime}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                                foreach (var recipient in Setting.DiEmailRecipients)
                                {
                                    try
                                    {
                                        await notificationService.SendMailAsync(
                                            recipient,
                                            Setting.DiEmailSubject,
                                            emailBody,
                                            CancellationToken.None
                                        );
                                        logger.LogInformation($"已發送DI變化通知電子郵件至 {recipient}");
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogError(ex, $"發送DI變化通知電子郵件至 {recipient} 時發生錯誤");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error when UpdateDiStatus");
            }
        }
        public async Task UpdateDoStatus(TwentyFourDioDevice device, IEnumerable<bool> values)
        {
            try
            {
                if (values.Count() == 8)
                {
                    for (int i = 0; i < 8; i++)
                    {
                        var newValue = values.Skip(i).First();
                        if (dataStore.CoilDiscretes[i + device.DoStartAddress] != newValue)
                        {
                            logger.LogInformation($" Do Modbus: {i + device.DoStartAddress} / 24dio: {device.Ip}@{i} Change to: {newValue}");
                            await UpdateStatusAsync(ModbusAddressType.Coil, (ushort)(i + device.DoStartAddress), newValue);

                        }

                    }
                }
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error when UpdateDoStatus");
            }
        }
    }
}

