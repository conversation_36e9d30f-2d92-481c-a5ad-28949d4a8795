using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TwentyFourDioGateway.SafetyMsg
{
    /// <summary>
    /// 電子郵件訊息模型，用於背景佇列處理
    /// </summary>
    public class EmailMessage
    {
        /// <summary>
        /// 收件人電子郵件地址
        /// </summary>
        public string Recipient { get; set; } = string.Empty;

        /// <summary>
        /// 郵件主旨
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// 郵件內容
        /// </summary>
        public string Body { get; set; } = string.Empty;

        /// <summary>
        /// 郵件建立時間
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 重試次數
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重試次數
        /// </summary>
        public const int MaxRetryCount = 3;
    }
} 