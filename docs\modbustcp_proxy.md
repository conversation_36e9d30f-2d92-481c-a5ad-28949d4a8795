有些設備經不起快速的modbustcp輪詢，會產生當機的狀況，所以做一個proxy將快速的modbustcp請求轉成慢速的請求，需求如下：
1. 批量的輪詢遠端ModbusTCP Slave，將遠端的狀態同步回本地的ModbusTCP Slave，程式內應該有現有的ModbusTCP Master可以使用，請評估是否適合直接使用。
2. 需可以輪詢遠端的coil,di,holding register,input register，可以在device_mapping裡設定每個group，一個group代表一台remote ModbusTCP Slave，底下還要設定多組範例，每組範圍應至少包含：種類，遠端開始位址，遠端結束位址，對應的本地端開始位址每個GROUP要可以設定輪詢的速度，可以降速輪詢
3. 本地ModbusTCP Slave被寫入時，也要將狀態即時的寫入到對應的遠端Slave
4. 寫入的指令，應該穿插在輪詢之間，不能跟輪詢之間彼此影響，應該在輪詢過程中，完成當下那一筆問答以後，穿插寫入的指令
5. 本地端的狀態被寫入時，應該先不要變更狀態，等待遠端的狀態也跟著被變動之後，本地端的狀態才被變更。
6. 如果有要產生api請使用minimal api，不要使用controller
7. 每個ModbusTcp Slave Endpoint都應該可以設定一組參數可以控制每一個指令中間的最小間隔，避免指令下得太快。
8. COIL跟holding register都應該是可寫入的，即本地的modbustcp slave如果有被寫入狀態的話，應該同時變更遠端ModbusTCP Slave的狀態，如果本地端的狀態被改變，而遠端的狀態沒能成功改變的話，則本地端的態應該維持改變前的狀態，除非遠端的狀態更新了，才能更新本地端的狀態

以下是實務上使用的情境

1. 系統運行時，讀取需要做proxy的endpoint以及各項點位
2. 系統不停的在背景對這些endpoint的點位重複輪詢(永不停止)，如果發生異常或斷線，要不停的重試，確保本地狀態與遠端同步
3. 輪詢後的數值請調用gatewayservice裡的更新點位狀態，讓狀態可以保存在db裡，下一次開啟時會先從db讀取先前狀態
4. 本地ModbusTcp Slave或其它方式收到COIL或是HOLDING REGISTER狀態變化時，會同步更新到遠端
5. 本地的狀態變化時，應優先寫入至遠端，確定遠端有變化本地才可以變化，如果遠端沒變化，本地就不要變化