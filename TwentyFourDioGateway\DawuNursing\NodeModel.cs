﻿using System.Text;
using System.Text.Json.Serialization;

namespace TwentyFourDioGateway.DawuNursing
{
    public class NodeModel
    {
        public string Ip { get; set; } = string.Empty;
        public ushort Index { get; set; }
        public string Floor { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string PrName { get; set; } = string.Empty;
        public int SubnetChannelId { get; set; } = 1;
        public int ManualConfirmAddress { get; set; } = -1;
        public bool ManualConfirm { get; set; } = false;
        public int CallingType { get; set; } = 700;

        public string Id { get; set; } = string.Empty;
        public string DeviceId
        {
            get
            {
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Ip}@{Index}@{Position}"));
            }
        }
        public string DeviceIdRawString 
        {
            get
            {
                return Encoding.UTF8.GetString(Convert.FromBase64String(DeviceId));
            }
        }
        public string DisplayName { get; set; } = string.Empty;
        public string NurseStation { get; set; } = string.Empty;
        public bool Reversed { get; set; } = false;

    }
}
//"設備": "求救按鈕",
//        "樓層": "B1F",
//        "設備編號": "B1-4-A14",
//        "PrName": "血液透析科男廁押扣",
//        "DisplayName": "男廁押扣",
//        "NurseStationName": "血液透析護理站",
//        "Ip": "*************",
//        "Index": "11",
//        "Floor": "B1",
//        "Position": "B1F-4弱電箱",
//        "Name": "B1F血液透析科男廁押扣 B1-4-A14",
//        "NurseStation": "HD",
//        "Reversed": "false"