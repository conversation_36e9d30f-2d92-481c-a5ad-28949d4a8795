﻿using Modbus.Data;
using Modbus.Device;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace TwentyFourDioGateway;
public class ModbusTcpServer
{
    public delegate void ValuesChangedDelegate(object? sender, ModbusSlaveRequestEventArgs e);
    public event ValuesChangedDelegate valuesChanged;
    private byte _slaveId;
    private ushort _port;
    private IPAddress _ip;
    private DataStore? _dataStore;
    private ModbusSlave? _slave;
    private TcpListener? _tcpListener;
    private readonly ILogger<ModbusTcpServer>? _logger;
    private Task? _listenTask;
    private bool _isRunning = false;
    private readonly object _lock = new object();

    // 監控指標
    private DateTime _lastRequestTime = DateTime.MinValue;
    private DateTime _startTime = DateTime.MinValue;
    private long _totalRequestCount = 0;
    private long _errorCount = 0;
    private long _writeRequestCount = 0;
    private long _readRequestCount = 0;

    /// <summary>
    /// 建立 ModbusTCP 伺服器
    /// </summary>
    /// <param name="dataStore">Modbus 資料儲存</param>
    /// <param name="logger">日誌記錄器</param>
    /// <param name="slaveId">Slave ID</param>
    /// <param name="port">TCP 連接埠</param>
    /// <param name="ip">綁定 IP 位址</param>
    public ModbusTcpServer(
        DataStore dataStore,
        ILogger<ModbusTcpServer>? logger = null,
        byte slaveId = 1,
        ushort port = 502,
        string ip = "0.0.0.0"
        )
    {
        _dataStore = dataStore;
        _slaveId = slaveId;
        _port = port;
        _ip = IPAddress.Parse(ip);
        _logger = logger;
    }

    /// <summary>
    /// 啟動 ModbusTCP 伺服器監聽
    /// </summary>
    public async Task StartListen(CancellationToken token)
    {
        try
        {
            _logger?.LogInformation($"啟動 ModbusTCP Slave 服務，綁定位址: {_ip}:{_port}");

            lock (_lock)
            {
                if (_isRunning)
                {
                    _logger?.LogWarning("ModbusTCP Slave 服務已在運行中，忽略重複啟動請求");
                    return;
                }

                _tcpListener = new TcpListener(_ip, _port);
                _tcpListener.Start();
                _slave = ModbusTcpSlave.CreateTcp(_slaveId, _tcpListener);
                _slave.DataStore = _dataStore;

                _slave.ModbusSlaveRequestReceived += _slave_ModbusSlaveRequestReceived;
                _slave.WriteComplete += _slave_WriteComplete;

                _listenTask = _slave.ListenAsync();
                _isRunning = true;
                _startTime = DateTime.Now;
                _lastRequestTime = DateTime.Now; // 初始化為當前時間

                // 重置計數器
                Interlocked.Exchange(ref _totalRequestCount, 0);
                Interlocked.Exchange(ref _errorCount, 0);
                Interlocked.Exchange(ref _writeRequestCount, 0);
                Interlocked.Exchange(ref _readRequestCount, 0);

                _logger?.LogInformation($"ModbusTCP Slave 服務已啟動，監聽位址: {_ip}:{_port}");
            }

            // 監控服務狀態
            while (!token.IsCancellationRequested)
            {
                await Task.Delay(1000, token);

                // 檢查 _listenTask 是否已完成或發生異常
                if (_listenTask != null && (_listenTask.IsCompleted || _listenTask.IsFaulted || _listenTask.IsCanceled))
                {
                    if (_listenTask.IsFaulted && _listenTask.Exception != null)
                    {
                        _logger?.LogError(_listenTask.Exception, "ModbusTCP Slave 服務發生異常");
                    }
                    else
                    {
                        _logger?.LogWarning("ModbusTCP Slave 服務已停止運行");
                    }

                    // 重新啟動服務
                    await RestartService();
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "ModbusTCP Slave 服務啟動或運行過程中發生異常");
            throw;
        }
        finally
        {
            await StopService();
        }
    }

    /// <summary>
    /// 重新啟動 ModbusTCP 服務
    /// </summary>
    private async Task RestartService()
    {
        _logger?.LogInformation("正在重新啟動 ModbusTCP Slave 服務...");
        await StopService();

        try
        {
            lock (_lock)
            {
                _tcpListener = new TcpListener(_ip, _port);
                _tcpListener.Start();
                _slave = ModbusTcpSlave.CreateTcp(_slaveId, _tcpListener);
                _slave.DataStore = _dataStore;

                _slave.ModbusSlaveRequestReceived += _slave_ModbusSlaveRequestReceived;
                _slave.WriteComplete += _slave_WriteComplete;

                _listenTask = _slave.ListenAsync();
                _isRunning = true;
            }

            _logger?.LogInformation("ModbusTCP Slave 服務已成功重新啟動");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "重新啟動 ModbusTCP Slave 服務時發生異常");
        }
    }

    /// <summary>
    /// 停止 ModbusTCP 服務
    /// </summary>
    private async Task StopService()
    {
        lock (_lock)
        {
            if (!_isRunning)
            {
                return;
            }

            _logger?.LogInformation("正在停止 ModbusTCP Slave 服務...");

            try
            {
                // 移除事件處理器
                if (_slave != null)
                {
                    _slave.ModbusSlaveRequestReceived -= _slave_ModbusSlaveRequestReceived;
                    _slave.WriteComplete -= _slave_WriteComplete;
                }

                // 停止 TcpListener
                _tcpListener?.Stop();

                _isRunning = false;
                _logger?.LogInformation("ModbusTCP Slave 服務已停止");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "停止 ModbusTCP Slave 服務時發生異常");
            }
            finally
            {
                _slave = null;
                _tcpListener = null;
            }
        }
    }

    /// <summary>
    /// 處理 Modbus 寫入完成事件
    /// </summary>
    private void _slave_WriteComplete(object? sender, ModbusSlaveRequestEventArgs e)
    {
        try
        {
            // 更新監控指標
            _lastRequestTime = DateTime.Now;
            Interlocked.Increment(ref _totalRequestCount);
            Interlocked.Increment(ref _writeRequestCount);

            _logger?.LogDebug($"ModbusTCP Slave 收到寫入請求: Function={e.Message.FunctionCode}, Address={e.Message.SlaveAddress}");
            valuesChanged?.Invoke(sender, e);
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _errorCount);
            _logger?.LogError(ex, "處理 ModbusTCP 寫入請求時發生異常");
        }
    }

    /// <summary>
    /// 處理 Modbus 請求接收事件
    /// </summary>
    private void _slave_ModbusSlaveRequestReceived(object? sender, ModbusSlaveRequestEventArgs e)
    {
        // 更新監控指標
        _lastRequestTime = DateTime.Now;
        Interlocked.Increment(ref _totalRequestCount);

        // 判斷是讀取還是寫入請求
        if (e.Message.FunctionCode <= 4) // 讀取功能碼 (1-4)
        {
            Interlocked.Increment(ref _readRequestCount);
        }

        //_logger?.LogDebug($"ModbusTCP Slave 收到請求: Function={e.Message.FunctionCode}, Address={e.Message.SlaveAddress}");
    }

    /// <summary>
    /// 取得服務健康狀態資訊
    /// </summary>
    /// <returns>服務健康狀態資訊</returns>
    public ModbusServerHealthInfo GetHealthInfo()
    {
        return new ModbusServerHealthInfo
        {
            IsRunning = _isRunning,
            StartTime = _startTime,
            LastRequestTime = _lastRequestTime,
            TotalRequestCount = _totalRequestCount,
            ErrorCount = _errorCount,
            WriteRequestCount = _writeRequestCount,
            ReadRequestCount = _readRequestCount,
            Uptime = _startTime != DateTime.MinValue ? DateTime.Now - _startTime : TimeSpan.Zero,
            TimeSinceLastRequest = _lastRequestTime != DateTime.MinValue ? DateTime.Now - _lastRequestTime : TimeSpan.Zero
        };
    }
}
