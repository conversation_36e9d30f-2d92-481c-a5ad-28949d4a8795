﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using TwentyFourDioGateway.Data;

namespace TwentyFourDioGateway.IcmpMonitor
{
    public class FuhoBackgroundService : BackgroundService
    {
        private readonly Setting _options;
        private readonly DeviceMapping _deviceMapping;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<FuhoBackgroundService> _logger;
        public FuhoBackgroundService(
            IOptions<Setting> options,
            IOptions<DeviceMapping> deviceMapping,
            IServiceProvider serviceProvider, 
            ILogger<FuhoBackgroundService> logger)
        {
            _options = options!.Value;
            _deviceMapping = deviceMapping!.Value;
            _serviceProvider = serviceProvider;
            _logger = logger;
        }
    
      
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceProvider.CreateScope())
                    {
                        //var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var gatewayService = scope.ServiceProvider.GetRequiredService<GatewayService>();
                        // 獲取所有攝影機
                        var cameras = _deviceMapping.FuhoMotionDetectionCameras;

                        foreach (var camera in cameras)
                        {
                            var dbNode = await gatewayService.GetDbStatusAsync(Models.ModbusAddressType.DiscreteInput, camera.ModbusAddress);                          
                            // 檢查是否處於觸發狀態                            
                            if (dbNode is not null && dbNode.Value)
                            {

                                if ((DateTime.Now - dbNode.UpdateTime).TotalSeconds > _deviceMapping.FuhoMotionDetectionAlarmTimeoutSeconds)
                                {
                                    await gatewayService.UpdateStatusAsync(Models.ModbusAddressType.DiscreteInput, camera.ModbusAddress, false);
                                    _logger.LogInformation($"Fuho motion detection camera {camera.Id} triggers timeout, reset status");
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in FuhoBackgroundService");
                }

                // 等待1秒
                await Task.Delay(1000, stoppingToken);
            }
        }

      
    }
}
