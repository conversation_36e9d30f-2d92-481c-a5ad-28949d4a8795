﻿﻿using TwentyFourDioGateway;
using System.Diagnostics;
using System.Net;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Modbus.Data;

namespace TwentyFourDioGateway;

/// <summary>
/// ModbusTCP 伺服器服務，負責啟動和管理 ModbusTCP Slave 服務
/// </summary>
public class ModbusTcpServerService : IHostedService
{
    private List<Task> _tasks = new List<Task>();
    private readonly CancellationTokenSource _cts = new CancellationTokenSource();
    private readonly Setting _setting;
    private readonly GatewayService _gatewayService;
    private readonly ILogger<ModbusTcpServer> _modbusLogger;
    private readonly ILogger<ModbusTcpServerService> _logger;
    private ModbusTcpServer? _modbusServer;
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromMinutes(5);

    /// <summary>
    /// 建立 ModbusTCP 伺服器服務
    /// </summary>
    public ModbusTcpServerService(
        IOptions<Setting> options,
        GatewayService gatewayService,
        ILogger<ModbusTcpServerService> logger,
        ILogger<ModbusTcpServer> modbusLogger)
    {
        _setting = options.Value;
        _gatewayService = gatewayService;
        _logger = logger;
        _modbusLogger = modbusLogger;
    }

    /// <summary>
    /// 啟動 ModbusTCP 伺服器服務
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation($"正在啟動 ModbusTCP 伺服器服務，綁定位址: {_setting.ModbusTCPSlaveBindIpAddress}:{_setting.ModbusTCPSlaveBindPort}");

        try
        {
            // 建立 ModbusTCP 伺服器
            _modbusServer = new ModbusTcpServer(
                _gatewayService.dataStore,
                _modbusLogger,
                ip: _setting.ModbusTCPSlaveBindIpAddress,
                port: _setting.ModbusTCPSlaveBindPort);

            // 註冊值變更事件處理器
            _modbusServer.valuesChanged += ModbusServer_valuesChanged;

            // 啟動 ModbusTCP 伺服器
            var modbusTask = Task.Run(async () => await _modbusServer.StartListen(_cts.Token), _cts.Token);
            _tasks.Add(modbusTask);

            // 啟動健康檢查任務
            var healthCheckTask = Task.Run(async () => await PerformHealthCheck(_cts.Token), _cts.Token);
            _tasks.Add(healthCheckTask);

            _logger.LogInformation("ModbusTCP 伺服器服務已啟動");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "啟動 ModbusTCP 伺服器服務時發生異常");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止 ModbusTCP 伺服器服務
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在停止 ModbusTCP 伺服器服務...");

        try
        {
            // 取消所有任務
            _cts.Cancel();

            // 等待所有任務完成
            await Task.WhenAll(_tasks);

            // 移除事件處理器
            if (_modbusServer != null)
            {
                _modbusServer.valuesChanged -= ModbusServer_valuesChanged;
            }

            _logger.LogInformation("ModbusTCP 伺服器服務已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止 ModbusTCP 伺服器服務時發生異常");
        }
    }

    /// <summary>
    /// 處理 ModbusTCP 值變更事件
    /// </summary>
    private void ModbusServer_valuesChanged(object? sender, Modbus.Device.ModbusSlaveRequestEventArgs e)
    {
        try
        {
            _gatewayService.ModbusServer_valuesChanged(sender, e);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "處理 ModbusTCP 值變更事件時發生異常");
        }
    }

    /// <summary>
    /// 執行 ModbusTCP 服務健康檢查
    /// </summary>
    private async Task PerformHealthCheck(CancellationToken token)
    {
        _logger.LogInformation("ModbusTCP 服務健康檢查任務已啟動");

        // 無活動超時時間 (10分鐘)
        TimeSpan inactivityTimeout = TimeSpan.FromMinutes(10);

        while (!token.IsCancellationRequested)
        {
            try
            {
                // 等待指定的健康檢查間隔
                await Task.Delay(_healthCheckInterval, token);

                // 檢查服務是否存在
                if (_modbusServer == null)
                {
                    _logger.LogWarning("ModbusTCP 服務不存在，無法執行健康檢查");
                    continue;
                }

                // 獲取健康狀態資訊
                var healthInfo = _modbusServer.GetHealthInfo();

                // 記錄健康檢查日誌
                _logger.LogInformation(
                    "ModbusTCP 服務健康狀態: 運行={IsRunning}, 運行時間={Uptime}, " +
                    "總請求數={TotalRequests}, 讀取請求={ReadRequests}, 寫入請求={WriteRequests}, 錯誤數={Errors}, " +
                    "最後請求時間={LastRequestTime}, 距離上次請求={TimeSinceLastRequest}",
                    healthInfo.IsRunning,
                    healthInfo.Uptime.ToString(@"d\.hh\:mm\:ss"),
                    healthInfo.TotalRequestCount,
                    healthInfo.ReadRequestCount,
                    healthInfo.WriteRequestCount,
                    healthInfo.ErrorCount,
                    healthInfo.LastRequestTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    healthInfo.TimeSinceLastRequest.ToString(@"hh\:mm\:ss"));

                // 檢查服務是否長時間沒有請求
                if (healthInfo.IsRunning &&
                    healthInfo.LastRequestTime != DateTime.MinValue &&
                    healthInfo.TimeSinceLastRequest > inactivityTimeout)
                {
                    _logger.LogWarning(
                        "ModbusTCP 服務長時間沒有請求 ({TimeSinceLastRequest})，可能存在連線問題",
                        healthInfo.TimeSinceLastRequest.ToString(@"hh\:mm\:ss"));

                    // 這裡可以添加重啟服務的邏輯，但由於 ModbusTcpServer 已經有自動重啟機制，
                    // 所以這裡只記錄警告日誌
                }

                // 檢查錯誤率
                if (healthInfo.TotalRequestCount > 0)
                {
                    double errorRate = (double)healthInfo.ErrorCount / healthInfo.TotalRequestCount;
                    if (errorRate > 0.1) // 錯誤率超過 10%
                    {
                        _logger.LogWarning(
                            "ModbusTCP 服務錯誤率較高 ({ErrorRate:P2})，總請求數={TotalRequests}，錯誤數={Errors}",
                            errorRate,
                            healthInfo.TotalRequestCount,
                            healthInfo.ErrorCount);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，不需處理
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "執行 ModbusTCP 服務健康檢查時發生異常");
            }
        }

        _logger.LogInformation("ModbusTCP 服務健康檢查任務已停止");
    }
}