# RS485 冷氣控制通訊協議說明

## 1. 通訊基礎

* **介面**: RS485
* **指令格式**: 所有指令均為9個位元組 (bytes) 的十六進制 (Hex) 碼。
* **資料位元組順序**: 從左至右，B1 為第一個位元組，B9 為第九個位元組。

## 2. 通用指令結構

所有發送的指令（啟動、停止、查詢）遵循以下基本結構：

`B1 B2 B3 B4 B5 B6 B7 B8 B9`

* **B1**: `0x43` (固定)
* **B2**: `0x50` (固定)
* **B3**: `0x53` (固定)
* **B4**: `0x05` (固定，可能為指令類型或長度相關)
* **B5**: `ID` (Device ID - 空調機號)
    * 1號機: `0x01`
    * ...
    * 10號機: `0x0A`
    * 54號機: `0x36`
    * 以此類推，為1個位元組的十六進制值。
* **B6**: `0x01` (固定，根據所有範例)
* **B7**: `0x21` (固定，根據所有範例)
* **B8**: `CMD` (Command Code / Function Code - 指令功能碼)
    * 啟動: `0x01`
    * 停止: `0x11`
    * 查詢: `0x50`
* **B9**: `CS` (Checksum - 校驗碼)

## 3. 校驗碼 (Checksum - CS) 計算邏輯 (適用於所有發送指令 B1-B9)

校驗碼是確保指令完整性的重要部分。其計算方式如下：

1.  **計算前8個位元組的總和 (S8)**:
    `S8 = B1 + B2 + B3 + B4 + B5 (ID) + B6 + B7 + B8 (CMD)`
    (所有值均為十六進制，進行十六進制加法運算)

2.  **計算校驗碼 (CS)**:
    `CS = (0x1E6 - S8) & 0xFF`
    * `0x1E6` 是一個固定的目標和值。
    * 用 `0x1E6` 減去計算出的 `S8`。
    * 取結果的最低有效位元組 (即 `AND 0xFF`) 作為 `CS`。
    * 簡單來說，`B1 + B2 + B3 + B4 + B5 + B6 + B7 + B8 + CS` 的總和，其最低位元組應為 `0xE6`。

**S8 計算的簡化:**
由於 B1, B2, B3, B4, B6, B7 是固定的，我們可以先計算它們的和：
`S_fixed = 0x43 + 0x50 + 0x53 + 0x05 + 0x01 + 0x21 = 0x10D`
所以，`S8 = 0x10D + ID + CMD`

## 4. 指令詳解

#### 4.1 啟動 (Start) 指令

* **目的**: 開啟指定ID的冷氣機。
* **指令功能碼 (CMD / B8)**: `0x01`
* **指令結構**: `43 50 53 05 ID 01 21 01 CS`
* **S8 計算**: `S8_start = 0x10D + ID + 0x01 = 0x10E + ID`
* **CS 計算**: `CS_start = (0x1E6 - S8_start) & 0xFF`

**範例 (啟動1號機 - ID `0x01`)**:
* `S8_start = 0x10E + 0x01 = 0x10F`
* `CS_start = (0x1E6 - 0x10F) & 0xFF = 0xD7`
* 指令: `43 50 53 05 01 01 21 01 D7`

**預期回應**:
提供的資料中未明確定義設備對「啟動」指令的具體RS485十六進制回應。在某些控制軟體界面上可能會顯示 "設備 reply ok(01)"，這可能代表高層次的指令接受確認。

#### 4.2 停止 (Stop) 指令

* **目的**: 關閉指定ID的冷氣機。
* **指令功能碼 (CMD / B8)**: `0x11`
* **指令結構**: `43 50 53 05 ID 01 21 11 CS`
* **S8 計算**: `S8_stop = 0x10D + ID + 0x11 = 0x11E + ID`
* **CS 計算**: `CS_stop = (0x1E6 - S8_stop) & 0xFF`

**範例 (停止1號機 - ID `0x01`)**:
* `S8_stop = 0x11E + 0x01 = 0x11F`
* `CS_stop = (0x1E6 - 0x11F) & 0xFF = 0xC7`
* 指令: `43 50 53 05 01 01 21 11 C7`

**預期回應**:
與「啟動」指令類似，未明確定義設備的具體RS485十六進制回應。

#### 4.3 查詢 (Query Status) 指令

* **目的**: 獲取指定ID冷氣機的目前運轉狀態。
* **指令功能碼 (CMD / B8)**: `0x50`
* **指令結構**: `43 50 53 05 ID 01 21 50 CS`
* **S8 計算**: `S8_query = 0x10D + ID + 0x50 = 0x15D + ID`
* **CS 計算**: `CS_query = (0x1E6 - S8_query) & 0xFF`

**範例 (查詢1號機 - ID `0x01`)**:
* `S8_query = 0x15D + 0x01 = 0x15E`
* `CS_query = (0x1E6 - 0x15E) & 0xFF = 0x88`
* 指令: `43 50 53 05 01 01 21 50 88`

**預期回應 (11 bytes)**:
設備會回覆一個11位元組的狀態信息，格式如下：
`R1 R2 R3 R4 R5 R6 R7 R8 R9 R10 R11`

* **R1**: `0x43` (回應標頭)
* **R2**: `0x50` (回應標頭)
* **R3**: `0x53` (回應標頭)
* **R4**: `0x07` (固定，與請求中的 `0x05` 不同)
* **R5**: `ID` (回應的設備ID，應與查詢的ID相同)
* **R6**: `0x03` (固定，與請求中的 `0x01` 不同，具體意義未知)
* **R7**: `0x21` (固定)
* **R8**: `0x50` (功能碼，表示是對查詢指令的回應)
* **R9**: `RE` (一個狀態位元組，具體意義在提供的資料中未明確，例如 `0x00`)
* **R10**: `CT` (**運轉狀態位元組 - Critical Status Byte**)
    * `0x00`: 冷氣關機中 (AC Off)
    * `0x01`: 冷氣開機中 (AC On)
* **R11**: `CS_response` (回應的校驗碼，其計算方式在提供的資料中未說明，解析時可記錄此值，但可能無法直接驗證)

**範例回應 (查詢54號機(`0x36`)，設備回覆其關機)**:
`43 50 53 07 36 03 21 50 00 00 4F`
* `R5 = 0x36`: 確認是54號機的回應。
* `R10 = 0x00`: 表示54號機目前為「關機中」。

## 5. 總結給AI的注意事項

1.  **嚴格遵循位元組順序與固定值。**
2.  **正確計算每個發送指令的校驗碼 (B9 / CS)，這是通訊成功的關鍵。**
    * `S8 = 0x10D + DeviceID (Hex) + CommandCode (Hex)`
    * `CS = (0x1E6 - S8) & 0xFF`
3.  **解析「查詢」回應時，重點關注第5個位元組 (R5) 以確認是哪個設備的回應，以及第10個位元組 (R10 / CT) 以獲取實際運轉狀態。**
4.  對於回應中的 `RE` (R9) 和 `CS_response` (R11)，以及 `R6 (0x03)`，記錄其值，但它們的深層意義或驗證方法目前不明。