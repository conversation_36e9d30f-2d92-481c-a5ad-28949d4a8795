using System.Net.Sockets;

namespace TwentyFourDioGateway;

/// <summary>
/// 空調控制客戶端，實現 RS485 冷氣控制通訊協議
/// 支援啟動、停止、查詢空調狀態功能
/// </summary>
public class KingmanAirClient : IDisposable
{
    private readonly string _remoteIp;
    private readonly ushort _remotePort;
    private readonly ILogger<KingmanAirClient> _logger;
    private TcpClient? _tcpClient;
    private NetworkStream? _networkStream;
    private readonly SemaphoreSlim _connectionSemaphore = new SemaphoreSlim(1, 1);
    private readonly AirConditionerCommandQueue _commandQueue;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private Task? _commandExecutorTask;
    private bool _disposed = false;
    private DateTime _lastConnectTime = DateTime.MinValue;
    private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(5);
    private readonly TimeSpan _queryTimeout = TimeSpan.FromSeconds(3); // 查詢超時時間
    private readonly TimeSpan _commandTimeout = TimeSpan.FromSeconds(3); // 一般指令超時時間
    private readonly TimeSpan _controlCommandTimeout = TimeSpan.FromSeconds(3); // 啟停控制指令超時時間（不等待回應，使用較短超時）
    private readonly TimeSpan _commandInterval = TimeSpan.FromMilliseconds(500); // 指令間隔時間
    private readonly TimeSpan _busSettleTime = TimeSpan.FromMilliseconds(1500); // BUS 穩定等待時間
    private DateTime _lastCommandTime = DateTime.MinValue; // 上次指令執行時間
    private DateTime _lastQueryTime = DateTime.MinValue; // 上次查詢指令時間

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="remoteIp">遠端IP位址</param>
    /// <param name="remotePort">遠端Port</param>
    /// <param name="logger">日誌記錄器</param>
    /// <param name="groupName">群組名稱</param>
    public KingmanAirClient(string remoteIp, ushort remotePort, ILogger<KingmanAirClient> logger, string groupName = "Unknown")
    {
        _remoteIp = remoteIp;
        _remotePort = remotePort;
        _logger = logger;

        // 建立指令佇列（需要 ILogger<AirConditionerCommandQueue>，暫時使用泛型轉換）
        var queueLogger = logger as ILogger<AirConditionerCommandQueue> ??
                         Microsoft.Extensions.Logging.Abstractions.NullLogger<AirConditionerCommandQueue>.Instance;
        _commandQueue = new AirConditionerCommandQueue(groupName, queueLogger);

        // 啟動指令執行器
        _commandExecutorTask = Task.Run(CommandExecutorAsync);
    }

    /// <summary>
    /// 是否已連線
    /// </summary>
    public bool IsConnected
    {
        get
        {
            return _tcpClient?.Connected == true && _networkStream != null;
        }
    }

    /// <summary>
    /// 連線到遠端空調控制器
    /// </summary>
    /// <returns>連線是否成功</returns>
    private async Task<bool> ConnectAsync()
    {
        if (_disposed)
            return false;

        // 檢查重連間隔
        if (DateTime.Now - _lastConnectTime < _reconnectInterval)
        {
            return false;
        }

        _lastConnectTime = DateTime.Now;

        try
        {
            // 清理舊連線
            await DisconnectAsync();

            _logger.LogDebug("Connecting to air conditioner controller: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);

            _tcpClient = new TcpClient();
            _tcpClient.ReceiveTimeout = 5000;
            _tcpClient.SendTimeout = 5000;

            await _tcpClient.ConnectAsync(_remoteIp, _remotePort);
            _networkStream = _tcpClient.GetStream();

            _logger.LogInformation("Successfully connected to air conditioner controller: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to air conditioner controller: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
            await DisconnectAsync();
            return false;
        }
    }

    /// <summary>
    /// 斷開連線
    /// </summary>
    private async Task DisconnectAsync()
    {
        try
        {
            if (_networkStream != null)
            {
                await _networkStream.DisposeAsync();
                _networkStream = null;
            }

            if (_tcpClient != null)
            {
                _tcpClient.Close();
                _tcpClient.Dispose();
                _tcpClient = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception occurred while disconnecting from air conditioner controller");
        }
    }

    /// <summary>
    /// 啟動空調（使用佇列）
    /// </summary>
    /// <param name="airConditionerId">空調機號 (1-255)</param>
    /// <returns>啟動是否成功</returns>
    public async Task<bool> StartAirConditionerAsync(byte airConditionerId)
    {
        var command = AirConditionerCommand.CreateControlCommand(airConditionerId, 0x01, "START");
        await _commandQueue.EnqueueAsync(command);

        try
        {
            var result = await command.TaskCompletionSource.Task;
            return (bool)result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start air conditioner: ID={AirConditionerId}", airConditionerId);
            return false;
        }
    }

    /// <summary>
    /// 停止空調（使用佇列）
    /// </summary>
    /// <param name="airConditionerId">空調機號 (1-255)</param>
    /// <returns>停止是否成功</returns>
    public async Task<bool> StopAirConditionerAsync(byte airConditionerId)
    {
        var command = AirConditionerCommand.CreateControlCommand(airConditionerId, 0x11, "STOP");
        await _commandQueue.EnqueueAsync(command);

        try
        {
            var result = await command.TaskCompletionSource.Task;
            return (bool)result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to stop air conditioner: ID={AirConditionerId}", airConditionerId);
            return false;
        }
    }

    /// <summary>
    /// 查詢空調狀態（使用佇列）
    /// </summary>
    /// <param name="airConditionerId">空調機號 (1-255)</param>
    /// <returns>空調狀態 (null=查詢失敗, true=開機中, false=關機中)</returns>
    public async Task<bool?> QueryAirConditionerStatusAsync(byte airConditionerId)
    {
        var command = AirConditionerCommand.CreateQueryCommand(airConditionerId);
        await _commandQueue.EnqueueAsync(command);

        try
        {
            var result = await command.TaskCompletionSource.Task;
            return result as bool?;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to query air conditioner status: ID={AirConditionerId}", airConditionerId);
            return null;
        }
    }

    /// <summary>
    /// 直接查詢空調狀態（不使用佇列）
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <returns>空調狀態 (null=查詢失敗, true=開機中, false=關機中)</returns>
    private async Task<bool?> QueryAirConditionerStatusDirectAsync(byte airConditionerId)
    {
        if (_disposed)
            return null;

        try
        {
            // 確保連線
            if (!IsConnected && !await ConnectAsync())
            {
                return null;
            }

            // 使用 SemaphoreSlim 確保同時只有一個查詢操作，並設定超時
            var semaphoreTask = _connectionSemaphore.WaitAsync(_queryTimeout);
            if (!await semaphoreTask)
            {
                _logger.LogWarning("Semaphore timeout while waiting to query air conditioner: ID={AirConditionerId}", airConditionerId);
                return null;
            }

            try
            {
                if (_networkStream == null)
                    return null;

                // 確保指令間隔
                await EnsureCommandIntervalAsync();

                // 建立查詢指令
                var queryCommand = BuildQueryCommand(airConditionerId);

                // 使用 CancellationToken 來控制超時
                using var cts = new CancellationTokenSource(_queryTimeout);

                try
                {
                    // 發送查詢指令
                    await _networkStream.WriteAsync(queryCommand, 0, queryCommand.Length, cts.Token);

                    // 讀取回應 (11 bytes) - 使用超時控制
                    var response = new byte[11];
                    var bytesRead = await ReadWithTimeoutAsync(_networkStream, response, 0, response.Length, cts.Token);

                    if (bytesRead >= 11)
                    {
                        // 解析回應
                        var status = ParseQueryResponse(response, airConditionerId);
                        if (status.HasValue)
                        {
                            // 不記錄成功查詢的 LOG，只有狀態變化時才在 KingmanAirService 中記錄
                            return status;
                        }
                        else
                        {
                            // 回應格式錯誤，需要清理通訊緩衝區並停頓
                            _logger.LogWarning("Invalid response format from air conditioner: ID={AirConditionerId}, flushing buffer and pausing", airConditionerId);
                            await FlushNetworkBufferAndPauseAsync();
                            return null;
                        }
                    }
                    else if (bytesRead == 0)
                    {
                        _logger.LogWarning("No response received from air conditioner: ID={AirConditionerId}", airConditionerId);
                        return null;
                    }
                    else
                    {
                        _logger.LogWarning("Incomplete response received from air conditioner: ID={AirConditionerId}, BytesRead={BytesRead}, flushing buffer and pausing",
                            airConditionerId, bytesRead);
                        await FlushNetworkBufferAndPauseAsync();
                        return null;
                    }
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Query timeout for air conditioner: ID={AirConditionerId}, Timeout={Timeout}ms",
                        airConditionerId, _queryTimeout.TotalMilliseconds);
                    return null;
                }
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while querying air conditioner status: ID={AirConditionerId}", airConditionerId);
            await DisconnectAsync(); // 發生異常時斷開連線，下次會自動重連
            return null;
        }
    }

    /// <summary>
    /// 發送空調控制指令
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <param name="commandCode">指令碼 (0x01=啟動, 0x11=停止)</param>
    /// <param name="commandName">指令名稱 (用於日誌)</param>
    /// <returns>指令是否成功</returns>
    private async Task<bool> SendCommandAsync(byte airConditionerId, byte commandCode, string commandName)
    {
        if (_disposed)
            return false;

        try
        {
            // 確保連線
            if (!IsConnected && !await ConnectAsync())
            {
                return false;
            }

            // 使用 SemaphoreSlim 確保同時只有一個指令操作，並設定超時（啟停指令使用較長超時）
            var semaphoreTask = _connectionSemaphore.WaitAsync(_controlCommandTimeout);
            if (!await semaphoreTask)
            {
                _logger.LogWarning("Semaphore timeout while waiting to send {CommandName} command to air conditioner: ID={AirConditionerId}",
                    commandName, airConditionerId);
                return false;
            }

            try
            {
                if (_networkStream == null)
                    return false;

                // 確保指令間隔
                await EnsureCommandIntervalAsync();

                // 建立指令
                var command = BuildControlCommand(airConditionerId, commandCode);

                // 使用 CancellationToken 來控制超時（啟停指令使用較長超時）
                using var cts = new CancellationTokenSource(_controlCommandTimeout);

                try
                {
                    // 發送指令
                    await _networkStream.WriteAsync(command, 0, command.Length, cts.Token);

                    _logger.LogInformation("✅ Successfully sent {CommandName} command to air conditioner: ID={AirConditionerId}",
                        commandName, airConditionerId);

                    // 等待並讀取控制指令回應
                    var success = await ReadAndValidateControlResponseAsync(airConditionerId, commandCode, commandName, cts.Token);

                    // 實際的狀態變更會透過後續的輪詢查詢來確認
                    return success;
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Command timeout while sending {CommandName} to air conditioner: ID={AirConditionerId}, Timeout={Timeout}ms",
                        commandName, airConditionerId, _controlCommandTimeout.TotalMilliseconds);
                    return false;
                }
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while sending {CommandName} command to air conditioner: ID={AirConditionerId}",
                commandName, airConditionerId);
            await DisconnectAsync(); // 發生異常時斷開連線，下次會自動重連
            return false;
        }
    }

    /// <summary>
    /// 建立控制指令 (啟動/停止)
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <param name="commandCode">指令碼</param>
    /// <returns>指令位元組陣列</returns>
    private static byte[] BuildControlCommand(byte airConditionerId, byte commandCode)
    {
        var command = new byte[9];

        // 固定標頭
        command[0] = 0x43; // B1
        command[1] = 0x50; // B2
        command[2] = 0x53; // B3
        command[3] = 0x05; // B4
        command[4] = airConditionerId; // B5 (ID)
        command[5] = 0x01; // B6
        command[6] = 0x21; // B7
        command[7] = commandCode; // B8 (CMD)

        // 計算校驗碼
        command[8] = CalculateChecksum(command);

        return command;
    }

    /// <summary>
    /// 建立查詢指令
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <returns>查詢指令位元組陣列</returns>
    private static byte[] BuildQueryCommand(byte airConditionerId)
    {
        return BuildControlCommand(airConditionerId, 0x50);
    }

    /// <summary>
    /// 計算校驗碼
    /// </summary>
    /// <param name="command">指令位元組陣列 (前8個位元組)</param>
    /// <returns>校驗碼</returns>
    private static byte CalculateChecksum(byte[] command)
    {
        // S8 = B1 + B2 + B3 + B4 + B5 + B6 + B7 + B8
        int s8 = 0;
        for (int i = 0; i < 8; i++)
        {
            s8 += command[i];
        }

        // CS = (0x1E6 - S8) & 0xFF
        int checksum = (0x1E6 - s8) & 0xFF;
        return (byte)checksum;
    }

    /// <summary>
    /// 帶超時的讀取方法
    /// </summary>
    /// <param name="stream">網路串流</param>
    /// <param name="buffer">緩衝區</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">讀取位元組數</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>實際讀取的位元組數</returns>
    private static async Task<int> ReadWithTimeoutAsync(NetworkStream stream, byte[] buffer, int offset, int count, CancellationToken cancellationToken)
    {
        var totalBytesRead = 0;
        var remainingBytes = count;
        var currentOffset = offset;

        // 持續讀取直到收到所有預期的位元組或超時
        while (totalBytesRead < count && !cancellationToken.IsCancellationRequested)
        {
            try
            {
                var bytesRead = await stream.ReadAsync(buffer, currentOffset, remainingBytes, cancellationToken);

                if (bytesRead == 0)
                {
                    // 連線已關閉
                    break;
                }

                totalBytesRead += bytesRead;
                currentOffset += bytesRead;
                remainingBytes -= bytesRead;
            }
            catch (OperationCanceledException)
            {
                // 超時或取消
                break;
            }
        }

        return totalBytesRead;
    }

    /// <summary>
    /// 清除控制指令的回應封包
    /// </summary>
    private async Task FlushControlResponseAsync()
    {
        if (_networkStream == null || !_networkStream.CanRead)
            return;

        try
        {
            // 清理網路緩衝區中的控制指令回應資料
            if (_networkStream.DataAvailable)
            {
                var flushBuffer = new byte[1024]; // 用於清理的緩衝區
                var totalFlushed = 0;
                var allFlushedData = new List<byte>(); // 收集所有清理的資料

                _logger.LogDebug("Flushing control response from network buffer, data available: {DataAvailable}", _networkStream.DataAvailable);

                // 持續讀取直到沒有更多資料
                while (_networkStream.DataAvailable && totalFlushed < 1024) // 最多清理 1KB
                {
                    try
                    {
                        using var cts = new CancellationTokenSource(200); // 200ms 超時
                        var bytesRead = await _networkStream.ReadAsync(flushBuffer, 0, flushBuffer.Length, cts.Token);

                        if (bytesRead == 0)
                            break;

                        // 收集這次讀取的資料
                        var currentData = new byte[bytesRead];
                        Array.Copy(flushBuffer, 0, currentData, 0, bytesRead);
                        allFlushedData.AddRange(currentData);

                        totalFlushed += bytesRead;

                        // 打印這次清理的資料（HEX 格式）
                        var hexString = Convert.ToHexString(currentData);
                        _logger.LogInformation("🗑️ Flushed control response chunk: {BytesRead} bytes - HEX: {HexData}",
                            bytesRead, hexString);
                    }
                    catch (OperationCanceledException)
                    {
                        // 清理超時，停止清理
                        break;
                    }
                }

                if (totalFlushed > 0)
                {
                    // 打印完整的清理資料（HEX 格式）
                    var completeHexString = Convert.ToHexString(allFlushedData.ToArray());
                    _logger.LogInformation("🗑️ Complete control response flushed: {TotalFlushed} bytes - Complete HEX: {CompleteHexData}",
                        totalFlushed, completeHexString);

                    // 分析控制指令回應格式
                    if (totalFlushed == 9)
                    {
                        var responseData = allFlushedData.ToArray();
                        _logger.LogInformation("🔍 Analyzing 9-byte control response: Header=[{B0:X2} {B1:X2} {B2:X2}] Length=[{B3:X2}] ID=[{B4:X2}] ResponseCode=[{B5:X2}] Data=[{B6:X2}] OriginalCmd=[{B7:X2}] CS=[{B8:X2}]",
                            responseData[0], responseData[1], responseData[2], responseData[3], responseData[4],
                            responseData[5], responseData[6], responseData[7], responseData[8]);

                        // 驗證回應格式
                        var isValidResponse = ValidateControlResponse(responseData);
                        _logger.LogInformation("🔍 Control response validation: {ValidationResult}",
                            isValidResponse ? "✅ Valid" : "❌ Invalid");
                    }
                    else if (totalFlushed == 11)
                    {
                        var responseData = allFlushedData.ToArray();
                        _logger.LogInformation("🔍 Analyzing 11-byte response: Header=[{B0:X2} {B1:X2} {B2:X2}] Type=[{B3:X2}] ID=[{B4:X2}] Data=[{B5:X2} {B6:X2} {B7:X2} {B8:X2} {B9:X2}] CS=[{B10:X2}]",
                            responseData[0], responseData[1], responseData[2], responseData[3], responseData[4],
                            responseData[5], responseData[6], responseData[7], responseData[8], responseData[9], responseData[10]);
                    }
                }
                else
                {
                    _logger.LogDebug("No control response data found in buffer");
                }
            }
            else
            {
                _logger.LogDebug("No control response data available in buffer");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception occurred while flushing control response");
        }
    }

    /// <summary>
    /// 驗證控制指令回應格式
    /// </summary>
    /// <param name="response">回應位元組陣列 (9 bytes)</param>
    /// <returns>是否為有效的控制指令回應</returns>
    private bool ValidateControlResponse(byte[] response)
    {
        if (response.Length != 9)
        {
            _logger.LogWarning("Invalid control response length: expected 9 bytes, got {Length}", response.Length);
            return false;
        }

        // 檢查固定標頭 (43 50 53)
        if (response[0] != 0x43 || response[1] != 0x50 || response[2] != 0x53)
        {
            _logger.LogWarning("Invalid control response header: {B0:X2} {B1:X2} {B2:X2}, expected 43 50 53",
                response[0], response[1], response[2]);
            return false;
        }

        // 檢查長度欄位 (應該是 05)
        if (response[3] != 0x05)
        {
            _logger.LogWarning("Invalid control response length field: {Length:X2}, expected 05", response[3]);
            return false;
        }

        // 檢查回應碼 (應該是 03)
        if (response[5] != 0x03)
        {
            _logger.LogWarning("Invalid control response code: {ResponseCode:X2}, expected 03", response[5]);
            return false;
        }

        // 檢查固定欄位 (應該是 21)
        if (response[6] != 0x21)
        {
            _logger.LogWarning("Invalid control response data field: {Data:X2}, expected 21", response[6]);
            return false;
        }

        // 驗證原始指令碼 (應該是 01 或 11)
        var originalCmd = response[7];
        if (originalCmd != 0x01 && originalCmd != 0x11)
        {
            _logger.LogWarning("Invalid original command code in response: {OriginalCmd:X2}, expected 01 or 11", originalCmd);
            return false;
        }

        // 驗證校驗碼
        var expectedChecksum = CalculateControlResponseChecksum(response);
        var actualChecksum = response[8];
        if (actualChecksum != expectedChecksum)
        {
            _logger.LogWarning("Control response checksum validation failed: got {Actual:X2}, expected {Expected:X2}",
                actualChecksum, expectedChecksum);
            return false;
        }

        _logger.LogDebug("Control response validation passed: DeviceID={DeviceId:X2}, ResponseCode={ResponseCode:X2}, OriginalCmd={OriginalCmd:X2}",
            response[4], response[5], response[7]);

        return true;
    }

    /// <summary>
    /// 計算控制指令回應的校驗碼
    /// </summary>
    /// <param name="response">回應位元組陣列 (前8個位元組)</param>
    /// <returns>校驗碼</returns>
    private static byte CalculateControlResponseChecksum(byte[] response)
    {
        // 控制指令回應的校驗碼計算：前8個位元組的總和
        int sum = 0;
        for (int i = 0; i < 8; i++)
        {
            sum += response[i];
        }

        // CS = (0x1E6 - sum) & 0xFF (與其他校驗碼計算方式相同)
        int checksum = (0x1E6 - sum) & 0xFF;
        return (byte)checksum;
    }

    /// <summary>
    /// 讀取並驗證控制指令回應
    /// </summary>
    /// <param name="expectedDeviceId">預期的設備ID</param>
    /// <param name="expectedCommandCode">預期的指令碼</param>
    /// <param name="commandName">指令名稱（用於日誌）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功驗證回應</returns>
    private async Task<bool> ReadAndValidateControlResponseAsync(byte expectedDeviceId, byte expectedCommandCode, string commandName, CancellationToken cancellationToken)
    {
        try
        {
            // 直接讀取 9 位元組的控制指令回應，使用超時機制
            var response = new byte[9];
            var bytesRead = await ReadWithTimeoutAsync(_networkStream, response, 0, response.Length, cancellationToken);

            if (bytesRead != 9)
            {
                _logger.LogWarning("❌ Incomplete control response for {CommandName}: expected 9 bytes, got {BytesRead}", commandName, bytesRead);

                // 如果回應不完整，清除剩餘資料避免影響後續通訊
                await FlushControlResponseAsync();
                return false;
            }

            // 打印回應的 HEX 內容
            var hexString = Convert.ToHexString(response);
            _logger.LogInformation("📥 Control response received for {CommandName}: 9 bytes - HEX: {HexData}", commandName, hexString);

            // 分析回應格式
            _logger.LogInformation("🔍 Analyzing control response: Header=[{B0:X2} {B1:X2} {B2:X2}] Length=[{B3:X2}] ID=[{B4:X2}] ResponseCode=[{B5:X2}] Data=[{B6:X2}] OriginalCmd=[{B7:X2}] CS=[{B8:X2}]",
                response[0], response[1], response[2], response[3], response[4],
                response[5], response[6], response[7], response[8]);

            // 驗證回應格式
            var isValid = ValidateControlResponse(response);

            // 額外檢查設備ID和指令碼是否匹配
            if (isValid)
            {
                if (response[4] != expectedDeviceId)
                {
                    _logger.LogWarning("❌ Device ID mismatch in control response: got {ActualId:X2}, expected {ExpectedId:X2}", response[4], expectedDeviceId);
                    isValid = false;
                }

                if (response[7] != expectedCommandCode)
                {
                    _logger.LogWarning("❌ Command code mismatch in control response: got {ActualCmd:X2}, expected {ExpectedCmd:X2}", response[7], expectedCommandCode);
                    isValid = false;
                }
            }

            if (isValid)
            {
                _logger.LogInformation("✅ Control response validation passed for {CommandName}: ID={DeviceId:X2}, Command={Command:X2}",
                    commandName, expectedDeviceId, expectedCommandCode);
                return true;
            }
            else
            {
                _logger.LogWarning("❌ Control response validation failed for {CommandName}: ID={DeviceId:X2}", commandName, expectedDeviceId);

                // 驗證失敗時清除可能的剩餘資料
                await FlushControlResponseAsync();
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Exception while reading control response for {CommandName}: ID={DeviceId}", commandName, expectedDeviceId);

            // 發生異常時清除可能的剩餘資料
            await FlushControlResponseAsync();
            return false;
        }
    }

    /// <summary>
    /// 停頓並清理網路緩衝區，避免錯誤封包影響後續通訊
    /// </summary>
    private async Task FlushNetworkBufferAndPauseAsync()
    {
        if (_networkStream == null || !_networkStream.CanRead)
            return;

        try
        {
            // 先停頓 800ms，讓 BUS 上的通訊封包該傳的傳完
            _logger.LogDebug("Pausing for 800ms to let BUS communication complete");
            await Task.Delay(800);

            // 然後清理網路緩衝區中的剩餘資料
            if (_networkStream.DataAvailable)
            {
                var flushBuffer = new byte[1024]; // 用於清理的緩衝區
                var totalFlushed = 0;

                _logger.LogDebug("Flushing network buffer, data available: {DataAvailable}", _networkStream.DataAvailable);

                // 持續讀取直到沒有更多資料
                while (_networkStream.DataAvailable && totalFlushed < 4096) // 最多清理 4KB 避免無限迴圈
                {
                    try
                    {
                        using var cts = new CancellationTokenSource(500); // 500ms 超時
                        var bytesRead = await _networkStream.ReadAsync(flushBuffer, 0, flushBuffer.Length, cts.Token);

                        if (bytesRead == 0)
                            break;

                        totalFlushed += bytesRead;
                        _logger.LogDebug("Flushed {BytesRead} bytes from network buffer, total flushed: {TotalFlushed}",
                            bytesRead, totalFlushed);
                    }
                    catch (OperationCanceledException)
                    {
                        // 清理超時，停止清理
                        break;
                    }
                }

                if (totalFlushed > 0)
                {
                    _logger.LogInformation("Network buffer flushed: {TotalFlushed} bytes removed to prevent communication errors", totalFlushed);
                }
                else
                {
                    _logger.LogDebug("No additional data found in buffer after pause");
                }
            }
            else
            {
                _logger.LogDebug("No data available in buffer after pause");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception occurred while pausing and flushing network buffer");
        }
    }

    /// <summary>
    /// 確保指令間隔，避免指令發送過於頻繁
    /// </summary>
    private async Task EnsureCommandIntervalAsync()
    {
        var timeSinceLastCommand = DateTime.Now - _lastCommandTime;
        if (timeSinceLastCommand < _commandInterval)
        {
            var waitTime = _commandInterval - timeSinceLastCommand;
            //_logger.LogDebug("Waiting {WaitTime}ms for command interval", waitTime.TotalMilliseconds);
            await Task.Delay(waitTime);
        }

        // 更新最後指令時間
        _lastCommandTime = DateTime.Now;
    }

    /// <summary>
    /// 確保 BUS 穩定後再發送控制指令
    /// 等待足夠時間讓所有輪詢指令完成並回應
    /// </summary>
    private async Task EnsureBusStabilityBeforeControlAsync()
    {
        try
        {
            // 計算距離上次查詢指令的時間
            var timeSinceLastQuery = DateTime.Now - _lastQueryTime;

            // 如果距離上次查詢時間太短，需要等待 BUS 穩定
            if (timeSinceLastQuery < _busSettleTime)
            {
                var waitTime = _busSettleTime - timeSinceLastQuery;
                _logger.LogInformation("Waiting {WaitTime}ms for BUS to settle before sending control command", waitTime.TotalMilliseconds);
                await Task.Delay(waitTime);
            }

            // 清理網路緩衝區，確保沒有殘留的回應資料
            await FlushNetworkBufferAndPauseAsync();

            _logger.LogDebug("BUS stability ensured, ready to send control command");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Exception occurred while ensuring BUS stability");
        }
    }

    /// <summary>
    /// 指令執行器，背景處理佇列中的指令
    /// </summary>
    private async Task CommandExecutorAsync()
    {
        _logger.LogInformation("Command executor started for air conditioner client: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);

        try
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // 從佇列取出下一個指令
                    var command = await _commandQueue.DequeueAsync();
                    if (command == null)
                    {
                        // 佇列為空，短暫等待
                        await Task.Delay(100, _cancellationTokenSource.Token);
                        continue;
                    }

                    // 等待執行權限
                    var timeout = command.CommandType == AirCommandType.Control ? _controlCommandTimeout : _queryTimeout;
                    if (!await _commandQueue.WaitForExecutionAsync(timeout))
                    {
                        command.TaskCompletionSource.SetException(new TimeoutException($"Failed to acquire execution permission within {timeout.TotalSeconds} seconds"));
                        continue;
                    }

                    try
                    {
                        // 如果是控制指令，需要先確保 BUS 穩定
                        if (command.CommandType == AirCommandType.Control)
                        {
                            await EnsureBusStabilityBeforeControlAsync();
                        }

                        // 執行指令
                        var result = await ExecuteCommandInternalAsync(command);
                        command.TaskCompletionSource.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        command.TaskCompletionSource.SetException(ex);
                    }
                    finally
                    {
                        // 釋放執行權限
                        _commandQueue.ReleaseExecution();
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常取消，退出迴圈
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Exception in command executor: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
                    // 繼續執行，不因單個錯誤而停止整個執行器
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Command executor terminated with exception: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
        }

        _logger.LogInformation("Command executor stopped for air conditioner client: {RemoteIp}:{RemotePort}", _remoteIp, _remotePort);
    }

    /// <summary>
    /// 內部執行指令方法
    /// </summary>
    private async Task<object?> ExecuteCommandInternalAsync(AirConditionerCommand command)
    {
        switch (command.CommandType)
        {
            case AirCommandType.Query:
                var queryResult = await QueryAirConditionerStatusDirectAsync(command.AirConditionerId);
                // 記錄查詢時間，用於控制指令的 BUS 穩定判斷
                _lastQueryTime = DateTime.Now;
                return queryResult;

            case AirCommandType.Control:
                if (!command.ControlCode.HasValue)
                    throw new ArgumentException("Control command must have ControlCode");

                var success = await SendCommandAsync(command.AirConditionerId, command.ControlCode.Value, command.CommandName);
                return success;

            default:
                throw new ArgumentException($"Unknown command type: {command.CommandType}");
        }
    }



    /// <summary>
    /// 解析查詢回應
    /// </summary>
    /// <param name="response">回應位元組陣列</param>
    /// <param name="expectedId">預期的空調機號</param>
    /// <returns>空調狀態 (null=解析失敗, true=開機中, false=關機中)</returns>
    private bool? ParseQueryResponse(byte[] response, byte expectedId)
    {
        if (response.Length < 11)
        {
            _logger.LogWarning("Response too short: expected 11 bytes, got {Length}", response.Length);
            return null;
        }

        // 檢查回應標頭
        if (response[0] != 0x43 || response[1] != 0x50 || response[2] != 0x53)
        {
            _logger.LogWarning("Invalid response header: {B1:X2} {B2:X2} {B3:X2}, expected 43 50 53",
                response[0], response[1], response[2]);
            return null;
        }

        // 檢查回應類型
        if (response[3] != 0x07)
        {
            _logger.LogWarning("Invalid response type: {Type:X2}, expected 07", response[3]);
            return null;
        }

        // 檢查設備ID
        if (response[4] != expectedId)
        {
            _logger.LogWarning("Device ID mismatch: got {ActualId}, expected {ExpectedId}",
                response[4], expectedId);
            return null;
        }

        // 檢查功能碼
        if (response[7] != 0x50)
        {
            _logger.LogWarning("Invalid function code: {FunctionCode:X2}, expected 50", response[7]);
            return null;
        }

        // 驗證校驗碼
        var expectedChecksum = CalculateResponseChecksum(response);
        var actualChecksum = response[10];
        if (actualChecksum != expectedChecksum)
        {
            _logger.LogWarning("Checksum validation failed for device {DeviceId}: got {Actual:X2}, expected {Expected:X2}",
                expectedId, actualChecksum, expectedChecksum);
            return null;
        }

        // 取得運轉狀態 (R10 / CT)
        var ctStatus = response[9];
        return ctStatus != 0x00; // 0x00=關機中, 0x01=開機中
    }

    /// <summary>
    /// 計算回應的校驗碼
    /// </summary>
    /// <param name="response">回應位元組陣列 (前10個位元組)</param>
    /// <returns>校驗碼</returns>
    private static byte CalculateResponseChecksum(byte[] response)
    {
        // 回應的校驗碼計算：前10個位元組的總和
        int sum = 0;
        for (int i = 0; i < 10; i++)
        {
            sum += response[i];
        }

        // CS = (0x1E6 - sum) & 0xFF (與指令校驗碼計算方式相同)
        int checksum = (0x1E6 - sum) & 0xFF;
        return (byte)checksum;
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;

            // 停止指令執行器
            _cancellationTokenSource.Cancel();

            // 等待指令執行器完成
            try
            {
                _commandExecutorTask?.Wait(TimeSpan.FromSeconds(5));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Exception while waiting for command executor to stop");
            }

            // 清理佇列
            _commandQueue.Dispose();

            // 斷開連線
            DisconnectAsync().Wait();

            // 釋放其他資源
            _connectionSemaphore.Dispose();
            _cancellationTokenSource.Dispose();
        }

        GC.SuppressFinalize(this);
    }
}
