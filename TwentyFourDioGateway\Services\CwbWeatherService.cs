using System.Net.Http.Json;
using Microsoft.Extensions.Options;
using TwentyFourDioGateway.Models;
using System.Text.Json.Serialization;
using System.Text.Json;

namespace TwentyFourDioGateway.Services
{
    public class CwbWeatherService : BackgroundService
    {
        private readonly ILogger<CwbWeatherService> _logger;
        private readonly HttpClient _httpClient;
        private readonly CwbWeatherConfig _config;
        private CwbWeatherData? _currentWeatherData;

        public CwbWeatherService(
            ILogger<CwbWeatherService> logger,
            IOptions<CwbWeatherConfig> config)
        {
            _logger = logger;
            _config = config.Value;
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri("https://opendata.cwa.gov.tw/");
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await UpdateWeatherDataAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating weather data");
                }

                await Task.Delay(TimeSpan.FromMinutes(_config.UpdateIntervalMinutes), stoppingToken);
            }
        }

        private async Task UpdateWeatherDataAsync()
        {
            var url = $"fileapi/v1/opendataapi/O-A0003-001?Authorization={_config.ApiKey}&downloadType=WEB&format=JSON";
            var response = await _httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to get weather data. Status code: {StatusCode}, Content: {Content}", 
                    response.StatusCode, 
                    await response.Content.ReadAsStringAsync());
                return;
            }

            var data = await response.Content.ReadFromJsonAsync<CwbApiResponse>();
            if (data?.Cwaopendata?.Dataset?.Station == null)
            {
                _logger.LogError("Invalid API response structure");
                return;
            }

            // _logger.LogDebug("Available stations: {stations}", 
            //     string.Join(", ", data.Cwaopendata.Dataset.Station.Select(s => s.StationName)));

            var station = data.Cwaopendata.Dataset.Station.FirstOrDefault(s => 
                s.StationName == _config.LocationName);

            if (station?.WeatherElement != null)
            {
                try 
                {
                    _currentWeatherData = new CwbWeatherData
                    {
                        ObservationTime = DateTime.Parse(station.ObsTime.DateTime),
                        Temperature = ParseDouble(station.WeatherElement.AirTemperature),
                        RelativeHumidity = ParseDouble(station.WeatherElement.RelativeHumidity),
                        RainfallMM = ParseDouble(station.WeatherElement.Now.Precipitation),
                        WindSpeed = ParseDouble(station.WeatherElement.WindSpeed),
                        WindDirection = GetWindDirection(ParseDouble(station.WeatherElement.WindDirection)),
                        WeatherDescription = station.WeatherElement.Weather
                    };

                    _logger.LogInformation("Weather data updated for {station}: Temperature {temp}°C, Humidity {humid}%, Rainfall {rain}mm",
                        station.StationName,
                        _currentWeatherData.Temperature,
                        _currentWeatherData.RelativeHumidity,
                        _currentWeatherData.RainfallMM);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error parsing weather data for station {station}", station.StationName);
                }
            }
            else
            {
                _logger.LogWarning("Station {locationName} not found in API response. Available stations: {stations}", 
                    _config.LocationName,
                    string.Join(", ", data.Cwaopendata.Dataset.Station.Select(s => s.StationName)));
            }
        }

        private double ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "-99" || value == "-990" || value == "-99.0" || value == "-990.0")
                return 0;
            
            return double.Parse(value);
        }

        private string GetWindDirection(double degrees)
        {
            string[] directions = { "北", "東北", "東", "東南", "南", "西南", "西", "西北" };
            int index = (int)Math.Round(degrees / 45.0) % 8;
            return directions[index];
        }

        public CwbWeatherData? GetCurrentWeather() => _currentWeatherData;
    }

    // API 回應的資料結構
    public class CwbApiResponse
    {
        [JsonPropertyName("cwaopendata")]
        public required Cwaopendata Cwaopendata { get; set; }
    }

    public class Cwaopendata
    {
        [JsonPropertyName("@xmlns")]
        public string? Xmlns { get; set; }

        [JsonPropertyName("identifier")]
        public string? Identifier { get; set; }

        [JsonPropertyName("sender")]
        public string? Sender { get; set; }

        [JsonPropertyName("sent")]
        public string? Sent { get; set; }

        [JsonPropertyName("status")]
        public string? Status { get; set; }

        [JsonPropertyName("msgType")]
        public string? MsgType { get; set; }

        [JsonPropertyName("dataid")]
        public string? DataId { get; set; }

        [JsonPropertyName("scope")]
        public string? Scope { get; set; }

        [JsonPropertyName("dataset")]
        public required Dataset Dataset { get; set; }
    }

    public class Dataset
    {
        [JsonPropertyName("Station")]
        public required List<Station> Station { get; set; }
    }

    public class Station
    {
        [JsonPropertyName("StationName")]
        public required string StationName { get; set; }

        [JsonPropertyName("StationId")]
        public required string StationId { get; set; }

        [JsonPropertyName("ObsTime")]
        public required ObsTime ObsTime { get; set; }

        [JsonPropertyName("GeoInfo")]
        public required GeoInfo GeoInfo { get; set; }

        [JsonPropertyName("WeatherElement")]
        public required WeatherElement WeatherElement { get; set; }
    }

    public class ObsTime
    {
        [JsonPropertyName("DateTime")]
        public required string DateTime { get; set; }
    }

    public class GeoInfo
    {
        [JsonPropertyName("TownCode")]
        public required string TownCode { get; set; }
    }

    public class WeatherElement
    {
        [JsonPropertyName("Weather")]
        public required string Weather { get; set; }

        [JsonPropertyName("Now")]
        public required Now Now { get; set; }

        [JsonPropertyName("WindDirection")]
        public required string WindDirection { get; set; }

        [JsonPropertyName("WindSpeed")]
        public required string WindSpeed { get; set; }

        [JsonPropertyName("AirTemperature")]
        public required string AirTemperature { get; set; }

        [JsonPropertyName("RelativeHumidity")]
        public required string RelativeHumidity { get; set; }
    }

    public class Now
    {
        [JsonPropertyName("Precipitation")]
        public required string Precipitation { get; set; }
    }
}