using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway.Services
{
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Configures CwbWeatherConfig from DeviceMapping section
        /// </summary>
        public static IServiceCollection AddCwbWeatherConfig(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<CwbWeatherConfig>(options =>
            {
                var deviceMapping = configuration.GetSection("DeviceMapping").Get<DeviceMapping>();
                if (deviceMapping != null)
                {
                    options.ApiKey = deviceMapping.CwbWeatherApiKey;
                    options.Location = deviceMapping.CwbWeatherLocation;
                    options.LocationName = deviceMapping.CwbWeatherLocationName;
                    options.UpdateIntervalMinutes = deviceMapping.CwbWeatherUpdateIntervalMinutes;
                }
            });

            return services;
        }
    }
}