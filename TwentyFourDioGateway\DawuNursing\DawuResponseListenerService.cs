﻿
using TwentyFourDioGateway;
using System.Diagnostics;
using System.Net;
using Microsoft.Extensions.Options;
using Modbus.Data;
using System.Net.Sockets;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text;
using System.IO.Pipelines;
using System.Text.Encodings.Web;
using System.Reflection;
using TwentyFourDioGateway.DawuNursing.Model;
using TwentyFourDioGateway.Models;
using Microsoft.Extensions.DependencyInjection;
using TwentyFourDioGateway.Data;

namespace TwentyFourDioGateway.DawuNursing;


public class DawuResponseListenerService : IHostedService
{
    private List<Task> _tasks = new List<Task>();
    private readonly CancellationTokenSource _cts = new CancellationTokenSource();
    private readonly Setting setting;
    private readonly DawuService dawuService;
    private readonly DawuSetting dawuSetting;
    private readonly ConcurrentQueue<RequestModel> ResponseQueue = new ConcurrentQueue<RequestModel>();
    private UdpClient? client;
    private Dictionary<int,UdpClient> clients = new Dictionary<int, UdpClient>();
    private IPAddress remoteIpAddress;
    private readonly GatewayService gatewayService;
    private readonly IServiceProvider serviceProvider;
    private readonly ILogger<DawuResponseListenerService> logger;
    public JsonSerializerOptions options = new JsonSerializerOptions
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    };
    public DawuResponseListenerService(
        ILogger<DawuResponseListenerService> logger,
        IOptions<Setting> options,IOptions<DawuSetting> dawuSettings ,DawuService dawuService, GatewayService gatewayService,
        IServiceProvider serviceProvider
        )
    {
        this.logger = logger;
        this.serviceProvider = serviceProvider;
        this.gatewayService = gatewayService;
        this.dawuService = dawuService;
        this.dawuSetting = dawuSettings.Value;
        setting = options.Value;
        //if (setting.EnablePingRongDawuNursing)
        //{
        //    //foreach (var subnetChannel in dawuSetting.SubnetChannels)
        //    //{
        //    //    clients.Add(IPAddress.Parse(subnetChannel.ResponseIp, IPAddress.Parse(subnetChannel.ResponseIp));
        //    //}
        //        remoteIpAddress = IPAddress.Parse(dawuSetting.ResponseIp);
        //}
    }
    private async Task ListenToAll(CancellationToken token)
    {

        UdpClient client = null;
        Task<UdpReceiveResult>? readTask = null;
        while (true)
        {
            try
            {
                if (token.IsCancellationRequested) return;
                if (client == null)
                {
                    try
                    {
                        //var ip = new IPEndPoint(IPAddress.Parse(subnetChannel.ResponseIp), subnetChannel.ResponsePort);
                        client = new UdpClient();
                        //client.ExclusiveAddressUse = false;
                        //client.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                        //client.Client.Bind(ip);
                        //client.Client.Bind(new IPEndPoint(IPAddress.Any, subnetChannel.ResponsePort));
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Udp Client exception subnet channel {e}");
                        await Task.Delay(600 * 5 * 1000, token);
                    }
                }
                try
                {
                    if (readTask == null)
                    {
                        readTask = client!.ReceiveAsync(token).AsTask();
                    }

                    //Console.WriteLine($"{DateTime.Now.ToString("mm:ss.fffff")} await");
                    var readResult = await readTask;
                    //Console.WriteLine($"{DateTime.Now.ToString("mm:ss.fffff")} awaited");
                    readTask = client!.ReceiveAsync(token).AsTask();
                    //var responseString = Encoding.ASCII.GetString(readResult.Buffer);
                    //var responseModel = JsonSerializer.Deserialize<RequestModel?>(responseString, options);


                    var responseString = Encoding.ASCII.GetString(readResult.Buffer);
                    var responseModel = JsonSerializer.Deserialize<RequestModel?>(responseString, options);
                    if (responseModel != null)
                    {
                        CheckResponse(responseModel, readResult.RemoteEndPoint.Address.ToString());
                    }

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }


                //await Task.Delay(10);
            }
            catch (Exception)
            {

            }
        }
    }
    public void CheckResponse(RequestModel model, string responseIp)
    {
        var subnetChannels = dawuSetting.SubnetChannels.Where(s => s.ResponseIp == responseIp).ToList();
        if (subnetChannels.Any())
        {
            var subnetChannel = subnetChannels.First();
                        
            if (model.Ip == subnetChannel.SourceIp)
            {
                ResponseQueue.Enqueue(model);
            }
            Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Responsed Queue Size: {ResponseQueue.Count}, {model}, ");
        }
    }

    private async Task Listening(SubnetChannel subnetChannel, CancellationToken token)
    {
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
        UdpClient client = null;
        Task<UdpReceiveResult>? readTask = null;
        while (true)
        {
            try
            {                
                if (token.IsCancellationRequested) return;
                //if (clients.Any(c => c.Value == null))
                //{
                //    var nullClients = clients.Where(c => c.Value == null).ToList();
                //    foreach (var nullClient in nullClients)
                //    {
                //        var subnetChannel = dawuSetting.SubnetChannels.Where(c => c.SubnetChannelId == nullClient.Key).FirstOrDefault();
                //        if (subnetChannel != null)
                //        {
                //            clients[nullClient.Key] = new UdpClient();
                //            clients[nullClient.Key].Client.Bind(new IPEndPoint(IPAddress.Parse(subnetChannel.ResponseIp), subnetChannel.ResponsePort));
                //        }
                //    }
                //}
                if (client == null)
                {
                    try
                    {
                        var ip = new IPEndPoint(IPAddress.Parse(subnetChannel.SourceIp), subnetChannel.ResponsePort);
                        client = new UdpClient(ip);
                        //Console.WriteLine("AA");
                        //client.ExclusiveAddressUse = false;
                        //client.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                        //client.Client.Bind(ip);
                        //client.Client.Bind(new IPEndPoint(IPAddress.Any, subnetChannel.ResponsePort));
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Udp Client exception subnet channel {subnetChannel.SubnetChannelId}, {e}");
                        await Task.Delay(600 * 5 * 1000, token);
                    }
                }
                try
                {
                    if (readTask == null)
                    {
                        readTask = client!.ReceiveAsync(token).AsTask();
                    }

                    //Console.WriteLine($"{DateTime.Now.ToString("mm:ss.fffff")} await");
                    var readResult = await readTask;
                    //Console.WriteLine($"{DateTime.Now.ToString("mm:ss.fffff")} awaited");
                    readTask = client!.ReceiveAsync(token).AsTask();
                    //var responseString = Encoding.ASCII.GetString(readResult.Buffer);
                    //var responseModel = JsonSerializer.Deserialize<RequestModel?>(responseString, options);



                    if (readResult.RemoteEndPoint.Address.ToString().Equals(subnetChannel.ResponseIp))
                    {
                        //Console.WriteLine(readResult.RemoteEndPoint.Address);
                        var responseString = Encoding.ASCII.GetString(readResult.Buffer);
                        var responseModel = JsonSerializer.Deserialize<RequestModel?>(responseString, options);
                        if (responseModel != null && responseModel.Ip == subnetChannel.SourceIp)
                        {
                            try
                            {
                                var db = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
                                await db.DawuResponses.AddAsync(new DawuResponse()
                                {
                                    DateTime = DateTime.Now,
                                    Body = responseString,
                                    SubnetChannelId = (byte)subnetChannel.SubnetChannelId
                                });
                                await db.SaveChangesAsync();
                            }
                            catch (Exception e)
                            {
                                logger.LogWarning(e, "Response save to db failed");
                            }
      
                            ResponseQueue.Enqueue(responseModel);
                        }
                        Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Responsed Queue Size: {ResponseQueue.Count}, {responseModel}, ");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }


                //await Task.Delay(10);
            }
            catch (Exception)
            {

            }
        }
    }
    private async Task ListeningAll(CancellationToken token)
    {
        List<Task> tasks = new List<Task>();
        foreach (var subnetChannel in dawuSetting.SubnetChannels)
        {
            tasks.Add(Listening(subnetChannel, token));
        }

        await Task.WhenAll(tasks);
    }

    private void deleteOrphans()
    {
        Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")} DeleteOrphans");
        if (dawuService.RequestQueue.Count > 0)
        {
            Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")} Deleted");
            ResponseQueue.Clear();
        }
    }
    
    private async Task CheckMatch(CancellationToken token)
    {
        Stopwatch sw = Stopwatch.StartNew();
        sw.Start();
        while (true)
        {
            try
            {
               
                if (token.IsCancellationRequested) return;

                while (dawuService.RequestQueue.TryDequeue(out var requestItem))
                {
                    var found = false;
                    while (ResponseQueue.TryDequeue(out var responseItem))
                    {
                        if (!responseItem.Equals(requestItem))
                        {
                            ResponseQueue.Enqueue(responseItem);
                        }
                        else
                        {                            
                            found = true;
                            var nurseStation = dawuSetting
                                .NurseStations                                
                                .FirstOrDefault(n => n.Name.Equals(requestItem.NurseStation));
                            if (nurseStation != null)
                            {                          
                                //db.DawuHistories
                                //    .Where(h => h.Body.Equals(JsonSerializer.Serialize(responseItem)))
                                //    .OrderByDescending(o => o.Id);
                                await gatewayService.UpdateStatusAsync(ModbusAddressType.DiscreteInput, (ushort)nurseStation.ModbusAddress, false);
                                //gatewayService.dataStore.InputDiscretes[nurseStation.ModbusAddress] = false;
                            }
                            Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Response Matched {requestItem}");
                            break;
                        }
                    }
                    var timeoutSeconds = (DateTime.UtcNow - DateTimeOffset.FromUnixTimeSeconds(requestItem.TimeStamp - 28800).UtcDateTime).TotalSeconds;
                    var timeouted = timeoutSeconds > dawuSetting.ResponseTimeoutSeconds;
                    if (found)
                    {
                        break;
                    }
                    if (timeouted)
                    {
                        var setTimeoutStatus = await SetTimeoutStatusToModbusAsync(requestItem);
                        Console.WriteLine($"{DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")}: Timeout Item: {requestItem}, Result : {setTimeoutStatus}");
                        break;
                    }
                    else
                    {
                        dawuService.RequestQueue.Enqueue(requestItem);
                    }
                }
                if (sw.Elapsed > TimeSpan.FromMinutes(5))
                {
                    deleteOrphans();
                    sw.Restart();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        

            await Task.Delay(1000,token);


        }
    }
    private async Task<bool> SetTimeoutStatusToModbusAsync(RequestModel model)
    {
        var nurseStation = dawuSetting
            .NurseStations
            .FirstOrDefault(n => n.Name.Equals(model.NurseStation));
        if (nurseStation != null)
        {
            //gatewayService.dataStore.InputDiscretes[nurseStation.ModbusAddress] = true;
            await gatewayService.UpdateStatusAsync(ModbusAddressType.DiscreteInput, (ushort)nurseStation.ModbusAddress, true);
        }
        return nurseStation != null;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        //var modbusServer = new ModbusTcpServer(gatewayService.dataStore, ip: setting.ModbusTCPSlaveBindIpAddress, port: setting.ModbusTCPSlaveBindPort);
        //modbusServer.valuesChanged += gatewayService.ModbusServer_valuesChanged;
        //var task = Task.Run(async () => await modbusServer.StartListen(_cts.Token), _cts.Token);
        if (setting.EnablePingRongDawuNursing)
        {
            _tasks.Add(ListeningAll(_cts.Token));
            _tasks.Add(CheckMatch(_cts.Token));

        }
        return Task.CompletedTask;
    }
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        await Task.WhenAll(_tasks);
    }
}