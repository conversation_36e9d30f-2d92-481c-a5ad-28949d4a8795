﻿using System.ComponentModel.DataAnnotations;

namespace TwentyFourDioGateway.Models
{
    public class RecordingRequest
    {
        [Key]
        public int Id { get; set; }
        public DateTime Time { get; set; } = DateTime.Now;
        public int VideoCamId { get; set; }
        public bool IsCompleted { get; set; } = false;
        public bool IsSuccessed { get; set; } = false;
        public int RecordDurationSeconds { get; set; }
        public string RecordUrl { get; set; } = string.Empty;


        public string BaUrl { get; set; } = string.Empty;
        public bool SendNotification { get; set; } = false;
        public string PublicVideoUrl { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string SmsReceivers { get; set; } = string.Empty;
        public string EmailReceivers { get; set; } = string.Empty;
        public bool IsNotificationCompleted { get; set; } = false;
        public bool IsNotificationSuccessed { get; set; } = false;

    }
}
