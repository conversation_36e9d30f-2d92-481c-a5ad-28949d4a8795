﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>true</InvariantGlobalization>

    <!-- 版本資訊 -->
    <Version>1.2.0</Version>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>1.2.0-$(Configuration)</InformationalVersion>

    <!-- 產品資訊 -->
    <Product>TwentyFourDio Gateway</Product>
    <Company>Weema Technology</Company>
    <Copyright>Copyright © Weema Technology 2024</Copyright>
    <Description>24DIO Gateway with ModbusTCP, DoToAo, and monitoring services</Description>
    <Authors>Weema Technology</Authors>

    <!-- Windows 檔案內容資訊 -->
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <IncludeSourceRevisionInInformationalVersion>false</IncludeSourceRevisionInInformationalVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="FFMpegCore" Version="5.2.0" />
    <PackageReference Include="IoC.Container" Version="1.3.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.2" />
    <PackageReference Include="NModbus4.Core" Version="1.0.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.3.1" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="DawuNursing\dawu_settings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="device_mapping.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="setting.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="run.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TwentyFourDioGateway.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="vlc.7z">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="ffmpeg.7z">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
