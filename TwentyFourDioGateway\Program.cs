using IoC;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyModel;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Events;
using System;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Channels;
using System.Xml;
using TwentyFourDioGateway;
using TwentyFourDioGateway.Data;
using TwentyFourDioGateway.DawuNursing;
using TwentyFourDioGateway.DawuNursing.Model;
using TwentyFourDioGateway.HiSharpAi;
using TwentyFourDioGateway.IcmpMonitor;
using TwentyFourDioGateway.Models;
using TwentyFourDioGateway.SafetyMsg;
using TwentyFourDioGateway.Services;
using Microsoft.AspNetCore.OpenApi;


//List<TwentyFourDioDevice> devs = new List<TwentyFourDioDevice>();
//for (ushort i = 0; i < 45; i++)
//{
//    var ip = $"192.168.2.{i + 201}";
//    devs.Add(new TwentyFourDioDevice()
//    {
//        DiStartAddress = (ushort)(i * 24),
//        DoStartAddress = (ushort)(i * 24),
//        Ip = ip,
//        Port = 5801
//    });
//}
//var options = new JsonSerializerOptions { WriteIndented = true };

//Console.Write(JsonSerializer.Serialize(devs, options));
//Console.ReadLine();

var builder = WebApplication.CreateBuilder(args);
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("Microsoft.EntityFrameworkCore", LogEventLevel.Warning)
    .MinimumLevel.Override("System.Net.Http.HttpClient", LogEventLevel.Warning)
    .MinimumLevel.Override("TwentyFourDioGateway.Services.AsteriskService", LogEventLevel.Information)
    .MinimumLevel.Override("System.Net.Http.HttpClient.TwentyFourDioGateway.Services.AsteriskService", LogEventLevel.Debug)
    .Enrich.FromLogContext()
    .WriteTo.Console(
        outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"
    )
    .WriteTo.Logger(lc => lc.Filter.ByIncludingOnly(e => e.Level == Serilog.Events.LogEventLevel.Information)
    .WriteTo.File($"./logs/log-{DateTime.Now:yyyy_MMdd_HHmm}-.log",
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}",
        rollingInterval: RollingInterval.Month,
        retainedFileCountLimit: int.MaxValue,
        flushToDiskInterval: TimeSpan.FromSeconds(1)))
    .WriteTo.Logger(lc => lc.Filter.ByIncludingOnly(e => e.Level == Serilog.Events.LogEventLevel.Debug)
    .WriteTo.File($"./logs/debug-{DateTime.Now:yyyy_MMdd_HHmm}-.log",
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}",
        rollingInterval: RollingInterval.Month,
        retainedFileCountLimit: int.MaxValue,
        flushToDiskInterval: TimeSpan.FromSeconds(1)))
    .WriteTo.Logger(lc => lc.Filter.ByIncludingOnly(e => e.Level == Serilog.Events.LogEventLevel.Error)
    .WriteTo.File($"./logs/error-{DateTime.Now:yyyy_MMdd_HHmm}-.log",
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}",
        rollingInterval: RollingInterval.Month,
        retainedFileCountLimit: int.MaxValue,
        flushToDiskInterval: TimeSpan.FromSeconds(1)))
    .CreateLogger();
builder.Host.UseSerilog();
builder.Services.AddLogging(opt =>
{
    opt.AddConsole();
});

// 🚀 優先顯示版本資訊 - 在所有其他初始化之前
// 先註冊 VersionService 以便早期使用
builder.Services.AddSingleton<TwentyFourDioGateway.Services.VersionService>();

// 建立臨時的服務提供者來取得版本資訊
using (var tempServiceProvider = builder.Services.BuildServiceProvider())
{
    var tempVersionService = tempServiceProvider.GetRequiredService<TwentyFourDioGateway.Services.VersionService>();
    var versionString = tempVersionService.GetVersionString();
    var detailedVersion = tempVersionService.GetDetailedVersionString();

    // 在控制台顯示版本資訊（最優先）
    Console.WriteLine();
    Console.WriteLine("=".PadRight(80, '='));
    Console.WriteLine($"🚀 {versionString}");
    Console.WriteLine("=".PadRight(80, '='));
    Console.WriteLine(detailedVersion);
    Console.WriteLine("=".PadRight(80, '='));
    Console.WriteLine();

    // 使用 Serilog 記錄版本資訊（第一條 LOG）
    Log.Information("🚀 Application starting: {VersionString}", versionString);
    Log.Information("📦 Build info: v{FileVersion} built on {BuildDate:yyyy-MM-dd HH:mm:ss} ({Configuration})",
        tempVersionService.CurrentVersion.FileVersion,
        tempVersionService.CurrentVersion.BuildDate,
        tempVersionService.CurrentVersion.Configuration);
    Log.Information("🖥️  Runtime info: {TargetFramework} on {OSVersion}",
        tempVersionService.CurrentVersion.TargetFramework,
        tempVersionService.CurrentVersion.OSVersion);
}

// Add CORS policy with AllowAll configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

builder.Configuration.AddJsonFile("device_mapping.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile("setting.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile(@"DawuNursing\dawu_settings.json", optional: true, reloadOnChange: true);
// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.Configure<DeviceMapping>(builder.Configuration.GetSection("DeviceMapping"));
builder.Services.Configure<Setting>(builder.Configuration.GetSection("Setting"));
builder.Services.Configure<DawuSetting>(builder.Configuration.GetSection("Dawu"));

// Use extension method to configure CwbWeatherConfig from DeviceMapping
builder.Services.AddCwbWeatherConfig(builder.Configuration);

builder.Services.AddHttpClient();
builder.Services.AddDbContext<AppDbContext>(optionsAction: options =>
{
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"));
});
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 註冊郵件Channel
builder.Services.AddSingleton(_ => Channel.CreateUnbounded<EmailMessage>(new UnboundedChannelOptions
{
    SingleReader = true,  // 單一讀取者（背景服務）
    SingleWriter = false  // 多個寫入者（可能有多個服務需要發送郵件）
}));

// 註冊核心服務
builder.Services.AddSingleton<GatewayService>();
builder.Services.AddSingleton<DawuService>();
builder.Services.AddTransient<NotificationService>();
// VersionService 已在上面早期註冊

// 條件式註冊 DO轉AO 服務
var settings = builder.Configuration.GetSection("Setting").Get<Setting>();
if (settings?.EnableDoToAo == true)
{
    builder.Services.AddSingleton<DoToAoService>();
    builder.Services.AddHostedService<DoToAoPollingService>();
}

// 條件式註冊空調控制服務
if (settings?.EnableKingmanAir == true)
{
    builder.Services.AddSingleton<KingmanAirService>();
    builder.Services.AddHostedService<KingmanAirPollingService>();
}

// 註冊郵件相關服務
builder.Services.AddHostedService<EmailBackgroundService>();  // 註冊郵件背景服務
builder.Services.AddHostedService<NotificationBackgroundService>();

// 註冊 ModbusTCP 服務
builder.Services.AddHostedService<ModbusTcpServerService>();

// 註冊 24DIO 相關服務
builder.Services.AddHostedService<TwentyFourDioPollingService>();
builder.Services.AddHostedService<DawuResponseListenerService>();

// 註冊視頻錄製相關服務
builder.Services.AddHostedService<VideoRecordingHostedService>();
builder.Services.AddSingleton<VideoRecordingService>();

// 註冊其他監控服務
builder.Services.AddHostedService<SafetyMessageBackgroundService>();
builder.Services.AddHostedService<FuhoBackgroundService>();
builder.Services.AddScoped<HundureCardListService>();
builder.Services.AddHostedService<IcmpEngineBackgroundService>();
builder.Services.AddHostedService<HISharpAiCameraBackgroundService>();

// 條件式註冊 CwbWeatherService
if (settings?.EnableCwbWeather == true)
{
    builder.Services.AddSingleton<CwbWeatherService>();
    builder.Services.AddHostedService(sp => sp.GetRequiredService<CwbWeatherService>());
}

// 條件式註冊 AsteriskService
if (settings?.EnableAsteriskMonitoring == true)
{
    // 註冊命名的HttpClient，並配置重用和連接管理選項
    builder.Services.AddHttpClient("AsteriskClient", client =>
    {
        client.Timeout = TimeSpan.FromSeconds(30);
    })
    .SetHandlerLifetime(TimeSpan.FromMinutes(5))  // 設定底層HttpHandler的生命週期
    .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
    {
        PooledConnectionLifetime = TimeSpan.FromMinutes(2),  // 連接在池中的最長生命週期
        MaxConnectionsPerServer = 10,                        // 每伺服器的最大連接數
        EnableMultipleHttp2Connections = true,               // 啟用HTTP/2支援
        ConnectTimeout = TimeSpan.FromSeconds(5)             // 連接超時時間
    });

    builder.Services.AddSingleton<TwentyFourDioGateway.Services.AsteriskService>();
    builder.Services.AddHostedService(sp => sp.GetRequiredService<TwentyFourDioGateway.Services.AsteriskService>());
}

var app = builder.Build();

// 版本資訊已在上面優先顯示
Log.Information("🔧 Application built successfully, starting services...");

app.UseStaticFiles();
// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Use CORS middleware with the "AllowAll" policy
app.UseCors("AllowAll");

var db = app.Services.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
//await db.Database.EnsureCreatedAsync();
await db.Database.MigrateAsync();
//var list = await db.Nodes.ToListAsync();

await app.Services.CreateScope().ServiceProvider.GetRequiredService<GatewayService>().RestoreModbusDataAsync();

//var db = app.Services.GetRequiredService<IServiceProvider>().GetRequiredService<AppDbContext>();
//var l = db.Nodes.ToList();
//Console.WriteLine(l.Count());

//var u = new UdpClient(new IPEndPoint(IPAddress.Any, 2269));
////Byte[] bytesEncode = System.Text.Encoding.UTF8.GetBytes(inputText); //���o UTF8 2�i�� Byte
////string resultEncode = Convert.ToBase64String(bytesEncode); // �ഫ Base64 ���ު�
//var options = new JsonSerializerOptions
//{
//    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
//    WriteIndented = true
//};
//var jstring = JsonSerializer.Serialize(new RequestModel()
//{
//    SipNumber = "Device01",
//    NurseStation = "HR",
//    DisplayName = Convert.ToBase64String(Encoding.UTF8.GetBytes("ĵ���W��")),
//    Mac = "AA:BB:CC:DD:EE:FF",
//    Ip = "***************",
//    Action = 01,
//}, options);
//Console.WriteLine(jstring);
//byte[] bytes = Encoding.ASCII.GetBytes(jstring);
//await u.SendAsync(bytes,bytes.Length,IPEndPoint.Parse("***************"));
//await Task.Delay(1000);
//jstring = JsonSerializer.Serialize(new RequestModel()
//{
//    SipNumber = "Device01",
//    NurseStation = "HR",
//    DisplayName = Convert.ToBase64String(Encoding.UTF8.GetBytes("ĵ���W��")),
//    Mac = "AA:BB:CC:DD:EE:FF",
//    Ip = "***************",
//    Action = 0,
//}, options);
//Console.WriteLine(jstring);
//bytes = Encoding.ASCII.GetBytes(jstring);
//await u.SendAsync(bytes, bytes.Length, IPEndPoint.Parse("***************"));
// 版本資訊 API
app.MapGet("/api/version", (TwentyFourDioGateway.Services.VersionService versionService) =>
{
    return Results.Ok(versionService.CurrentVersion);
})
.WithName("GetVersion")
.WithDescription("取得應用程式版本資訊")
.WithOpenApi();

app.MapGet("/api/version/simple", (TwentyFourDioGateway.Services.VersionService versionService) =>
{
    return Results.Ok(new
    {
        Version = versionService.CurrentVersion.Version,
        Product = versionService.CurrentVersion.Product,
        BuildDate = versionService.CurrentVersion.BuildDate,
        Configuration = versionService.CurrentVersion.Configuration
    });
})
.WithName("GetSimpleVersion")
.WithDescription("取得簡化的版本資訊")
.WithOpenApi();

app.MapGet("/ttt", () =>
{
    return Results.NotFound();
});
app.MapGet("/fuhoMotionDetection", async (string id, ILogger<Program> logger, IOptions<DeviceMapping> deviceMapping, GatewayService service) =>
{

    var cam = deviceMapping.Value.FuhoMotionDetectionCameras.FirstOrDefault(f => f.Id == id);
    if (cam != null)
    {
        var oldStatus = await service.GetDbStatusAsync(ModbusAddressType.DiscreteInput, cam.ModbusAddress);
        if (oldStatus?.Value ?? false)
        {
            logger.LogInformation($"Fuho motion detection exist event {id}");
        }
        else
        {
            logger.LogInformation($"Fuho motion detection new event {id}");
        }

        await service.UpdateStatusAsync(ModbusAddressType.DiscreteInput, cam.ModbusAddress, true);

        return Results.Ok();
    }
    else
    {
        logger.LogWarning($"fuhoAi id not found {id}");
    }
    return Results.NotFound();

});
app.MapGet("/test", (VideoRecordingService hiservice) =>
{
    //var hiservice = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<HiSharpVideoRecordingHostedService>();
    for (int i = 0; i < 5; i++)
    {
        hiservice.channel.Writer.TryWrite(new RecordingRequest() { Id = 1 , Time = DateTime.Now});

    }
    return "OK";
});
app.MapGet("/", async (AppDbContext db, IServiceProvider serviceProvider) =>
{
    var startTime = DateTime.Now.AddMinutes(-30);
    var endTime = startTime.AddDays(1);
    var httpClient = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IHttpClientFactory>().CreateClient();
    var options = new JsonSerializerOptions
    {
        Converters = { new HundureDateTimeJsonConverter() },
        WriteIndented = true
    };
    var command = new HundureGetPubEventRequestCommand
    {
        StartDateTime = startTime,
        EndDateTime = endTime,
        CmdSerial = "1"
    };
    var result = await httpClient.PostAsJsonAsync("http://***************:88/SyncECSService/GetPubEvent", command,options:options);
    result.EnsureSuccessStatusCode();

    var raw = await result.Content.ReadAsStringAsync();
    var response = await result.Content.ReadFromJsonAsync<HundureGetPubEventResponseModel>(options);
    if (response != null)
    {

        //if (response.EventCode_ID == "010001")
        //{

        //    //search cardno in card list, response.CardNo

        //    //if cardno is in card list, record video and send to the user
        //}
    }
    return new { response, command,raw };
    //    var list = await db.Nodes.ToListAsync();
    //    db.RemoveRange(list);
    //    await db.SaveChangesAsync();
    //    list = Enumerable.Range(1,100).Select(i => new Node()
    //    {
    //        Address = (ushort)i,
    //        AddressType = ModbusAddressType.Coil
    //    })
    //    .ToList();
    //    db.AddRange(list);
    //     list = Enumerable.Range(1, 100).Select(i => new Node()
    //    {
    //        Address = (ushort)i,
    //        AddressType = ModbusAddressType.DiscreteInput
    //    })
    //.ToList();
    //    db.AddRange(list);
    //    await db.SaveChangesAsync();
    //    return db.Nodes.Count();
});

// DI/DO State Management APIs
app.MapGet("/api/di-state", (GatewayService service) =>
{
    var dataStore = service.dataStore;
    var diState = dataStore.InputDiscretes.Select((value, index) => new
    {
        Address = index,
        Value = value
    }).ToDictionary(x => x.Address, x => x.Value);

    return Results.Ok(diState);
})
.WithName("GetDIState")
.WithDescription("讀取所有DI狀態，從DataStore而非資料庫")
.WithOpenApi();

app.MapGet("/api/do-state", (GatewayService service) =>
{
    var dataStore = service.dataStore;
    var doState = dataStore.CoilDiscretes.Select((value, index) => new
    {
        Address = index,
        Value = value
    }).ToDictionary(x => x.Address, x => x.Value);

    return Results.Ok(doState);
})
.WithName("GetDOState")
.WithDescription("讀取所有DO狀態，從DataStore而非資料庫")
.WithOpenApi();

// app.MapPut("/api/di-state/{address}", async (ushort address, bool value, GatewayService service) =>
// {
//     await service.UpdateStatusAsync(ModbusAddressType.DiscreteInput, address, value);
//     return Results.Ok(new { Address = address, Value = value });
// })
// .WithName("UpdateDIState")
// .WithDescription("寫入指定地址的DI狀態")
// .WithOpenApi();

app.MapPut("/api/do-state/{address}", (ushort address, bool value, GatewayService service) =>
{
    service.EnqueueWriteDoToTwentyFourDio(address, value);
    // await service.UpdateStatusAsync(ModbusAddressType.Coil, address, value);
    return Results.Ok(new { Address = address, Value = value });
})
.WithName("UpdateDOState")
.WithDescription("寫入指定地址的DO狀態，並保留原有更新之連動邏輯，目前僅支援24DIO的DO點。")
.WithOpenApi();

app.MapGet("/api/di-state/{startAddress}/{endAddress}", (ushort startAddress, ushort endAddress, GatewayService service) =>
{
    if (startAddress > endAddress)
    {
        return Results.BadRequest(new { Error = "startAddress cannot be greater than endAddress" });
    }

    var dataStore = service.dataStore;
    var diState = dataStore.InputDiscretes
        .Skip(startAddress)
        .Take(endAddress - startAddress+1)
        .Select((value, index) => new
        {
            Address = startAddress + index,
            Value = value
        })
        .ToDictionary(x => x.Address, x => x.Value);

    return Results.Ok(diState);
})
.WithName("GetDIStateRange")
.WithDescription("讀取指定範圍的DI狀態，從DataStore而非資料庫")
.WithOpenApi();

app.MapGet("/api/do-state/{startAddress}/{endAddress}", (ushort startAddress, ushort endAddress, GatewayService service) =>
{
    if (startAddress > endAddress)
    {
        return Results.BadRequest(new { Error = "startAddress cannot be greater than endAddress" });
    }

    var dataStore = service.dataStore;
    var doState = dataStore.CoilDiscretes
        .Skip(startAddress)
        .Take(endAddress - startAddress+1)
        .Select((value, index) => new
        {
            Address = startAddress + index,
            Value = value
        })
        .ToDictionary(x => x.Address, x => x.Value);

    return Results.Ok(doState);
})
.WithName("GetDOStateRange")
.WithDescription("讀取指定範圍的DO狀態，從DataStore而非資料庫")
.WithOpenApi();

app.MapGet("/api/weather", (ILogger<Program> logger, [FromServices] CwbWeatherService weatherService) =>
{
    var weather = weatherService.GetCurrentWeather();
    if (weather == null)
    {
        return Results.NotFound(new { Message = "Weather data not available yet" });
    }
    return Results.Ok(weather);
})
.WithName("GetWeather")
.WithDescription("取得目前的天氣資料")
.WithOpenApi();

// DO轉AO功能的Minimal API端點（條件式註冊）
if (settings?.EnableDoToAo == true)
{
    app.MapGet("/api/do-to-ao/status", (DoToAoService doToAoService, ILogger<Program> logger) =>
    {
        try
        {
            var status = doToAoService.GetConnectionStatus();
            return Results.Ok(status);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "取得DO轉AO連線狀態時發生異常");
            return Results.Problem("取得連線狀態時發生異常");
        }
    })
    .WithName("GetDoToAoStatus")
    .WithDescription("取得所有DO轉AO群組的連線狀態")
    .WithOpenApi();

    app.MapPost("/api/do-to-ao/reconnect", async (DoToAoService doToAoService, ILogger<Program> logger) =>
    {
        try
        {
            logger.LogInformation("收到手動重新連線請求");
            var results = await doToAoService.ReconnectAllAsync();
            return Results.Ok(results);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手動重新連線時發生異常");
            return Results.Problem("重新連線時發生異常");
        }
    })
    .WithName("ReconnectDoToAo")
    .WithDescription("手動重新連線所有ModbusTCP Master客戶端")
    .WithOpenApi();

    app.MapPost("/api/do-to-ao/test", async (ushort doAddress, bool value, DoToAoService doToAoService, ILogger<Program> logger) =>
    {
        try
        {
            logger.LogInformation("收到手動測試DO轉AO請求: DO位址={DoAddress}, 值={Value}", doAddress, value);
            var success = await doToAoService.ProcessDoChangeAsync(doAddress, value);

            return Results.Ok(new
            {
                DoAddress = doAddress,
                Value = value,
                Success = success,
                Message = success ? "測試成功" : "測試失敗"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手動測試DO轉AO時發生異常: DO位址={DoAddress}, 值={Value}", doAddress, value);
            return Results.Problem("測試時發生異常");
        }
    })
    .WithName("TestDoToAo")
    .WithDescription("手動測試DO轉AO功能")
    .WithOpenApi();

    app.MapPost("/api/do-to-ao/poll", async (DoToAoService doToAoService, ILogger<Program> logger) =>
    {
        try
        {
            logger.LogInformation("收到手動輪詢請求");
            var success = await doToAoService.PollAllGroupsAsync();

            return Results.Ok(new
            {
                Success = success,
                Message = success ? "輪詢成功" : "輪詢失敗"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手動輪詢時發生異常");
            return Results.Problem("輪詢時發生異常");
        }
    })
    .WithName("PollDoToAo")
    .WithDescription("手動觸發DO轉AO輪詢")
    .WithOpenApi();
}

// 空調控制相關 API（條件式註冊）
if (settings?.EnableKingmanAir == true)
{
    app.MapGet("/api/kingman-air/status", (KingmanAirService kingmanAirService) =>
    {
        var connectionStatus = kingmanAirService.GetConnectionStatus();
        return Results.Ok(new
        {
            ConnectionStatus = connectionStatus,
            Timestamp = DateTime.Now
        });
    })
    .WithName("GetKingmanAirStatus")
    .WithDescription("取得空調控制器連線狀態")
    .WithOpenApi();

    app.MapPost("/api/kingman-air/test", async (ushort doAddress, bool value, KingmanAirService kingmanAirService, ILogger<Program> logger) =>
    {
        try
        {
            logger.LogInformation("收到手動測試空調控制請求: DO位址={DoAddress}, 值={Value}", doAddress, value);
            var success = await kingmanAirService.ProcessDoChangeAsync(doAddress, value);

            return Results.Ok(new
            {
                DoAddress = doAddress,
                Value = value,
                Success = success,
                Message = success ? "測試成功" : "測試失敗"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手動測試空調控制時發生異常: DO位址={DoAddress}, 值={Value}", doAddress, value);
            return Results.Problem("測試時發生異常");
        }
    })
    .WithName("TestKingmanAir")
    .WithDescription("手動測試空調控制功能")
    .WithOpenApi();

    app.MapPost("/api/kingman-air/poll", async (KingmanAirService kingmanAirService, ILogger<Program> logger) =>
    {
        try
        {
            logger.LogInformation("收到手動輪詢空調狀態請求");
            var success = await kingmanAirService.PollAllGroupsAsync();

            return Results.Ok(new
            {
                Success = success,
                Message = success ? "輪詢成功" : "輪詢失敗"
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "手動輪詢空調狀態時發生異常");
            return Results.Problem("輪詢時發生異常");
        }
    })
    .WithName("PollKingmanAir")
    .WithDescription("手動觸發空調狀態輪詢")
    .WithOpenApi();
}

try
{
    await app.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
//app.Run();
