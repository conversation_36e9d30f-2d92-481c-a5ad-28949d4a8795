﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    /// <inheritdoc />
    public partial class add_safety_nofitication : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DisplayName",
                table: "HiSharpRecordingRequests",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "EmailReceivers",
                table: "HiSharpRecordingRequests",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsNotificationCompleted",
                table: "HiSharpRecordingRequests",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsNotificationSuccessed",
                table: "HiSharpRecordingRequests",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "SendNotification",
                table: "HiSharpRecordingRequests",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "SmsReceivers",
                table: "HiSharpRecordingRequests",
                type: "TEXT",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisplayName",
                table: "HiSharpRecordingRequests");

            migrationBuilder.DropColumn(
                name: "EmailReceivers",
                table: "HiSharpRecordingRequests");

            migrationBuilder.DropColumn(
                name: "IsNotificationCompleted",
                table: "HiSharpRecordingRequests");

            migrationBuilder.DropColumn(
                name: "IsNotificationSuccessed",
                table: "HiSharpRecordingRequests");

            migrationBuilder.DropColumn(
                name: "SendNotification",
                table: "HiSharpRecordingRequests");

            migrationBuilder.DropColumn(
                name: "SmsReceivers",
                table: "HiSharpRecordingRequests");
        }
    }
}
