namespace TwentyFourDioGateway;

/// <summary>
/// 空調指令類型
/// </summary>
public enum AirCommandType
{
    /// <summary>
    /// 查詢狀態指令（低優先權）
    /// </summary>
    Query = 0,
    
    /// <summary>
    /// 啟停控制指令（高優先權）
    /// </summary>
    Control = 1
}

/// <summary>
/// 空調指令
/// </summary>
public class AirConditionerCommand
{
    /// <summary>
    /// 指令類型
    /// </summary>
    public AirCommandType CommandType { get; set; }
    
    /// <summary>
    /// 空調機號
    /// </summary>
    public byte AirConditionerId { get; set; }
    
    /// <summary>
    /// 控制指令碼（僅控制指令使用）
    /// </summary>
    public byte? ControlCode { get; set; }
    
    /// <summary>
    /// 指令名稱（用於日誌）
    /// </summary>
    public string CommandName { get; set; } = string.Empty;
    
    /// <summary>
    /// 任務完成源
    /// </summary>
    public TaskCompletionSource<object?> TaskCompletionSource { get; set; } = new();
    
    /// <summary>
    /// 指令建立時間
    /// </summary>
    public DateTime CreatedTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 優先權（數字越大優先權越高）
    /// </summary>
    public int Priority => (int)CommandType;
    
    /// <summary>
    /// 建立查詢指令
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <returns>查詢指令</returns>
    public static AirConditionerCommand CreateQueryCommand(byte airConditionerId)
    {
        return new AirConditionerCommand
        {
            CommandType = AirCommandType.Query,
            AirConditionerId = airConditionerId,
            CommandName = "Query"
        };
    }
    
    /// <summary>
    /// 建立控制指令
    /// </summary>
    /// <param name="airConditionerId">空調機號</param>
    /// <param name="controlCode">控制指令碼</param>
    /// <param name="commandName">指令名稱</param>
    /// <returns>控制指令</returns>
    public static AirConditionerCommand CreateControlCommand(byte airConditionerId, byte controlCode, string commandName)
    {
        return new AirConditionerCommand
        {
            CommandType = AirCommandType.Control,
            AirConditionerId = airConditionerId,
            ControlCode = controlCode,
            CommandName = commandName
        };
    }
}
