﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TwentyFourDioGateway.Data;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20230510075513_init6")]
    partial class init6
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "7.0.5");

            modelBuilder.Entity("TwentyFourDioGateway.Models.DawuHistory", b =>
                {
                    b.Property<ulong>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Response")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ResponseDateTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<byte>("SubnetChannelId")
                        .HasColumnType("INTEGER");

                    b.Property<byte>("TwentyFourDioIndex")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TwentyFourDioIp")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("DawuHistories");
                });

            modelBuilder.Entity("TwentyFourDioGateway.Models.Node", b =>
                {
                    b.Property<ushort>("Address")
                        .HasColumnType("INTEGER");

                    b.Property<int>("AddressType")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Value")
                        .HasColumnType("INTEGER");

                    b.HasKey("Address", "AddressType");

                    b.ToTable("Nodes");
                });
#pragma warning restore 612, 618
        }
    }
}
