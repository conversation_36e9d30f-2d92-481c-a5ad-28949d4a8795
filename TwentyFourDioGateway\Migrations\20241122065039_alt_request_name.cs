﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    /// <inheritdoc />
    public partial class alt_request_name : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HiSharpRecordingRequests");

            migrationBuilder.CreateTable(
                name: "RecordingRequests",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Time = table.Column<DateTime>(type: "TEXT", nullable: false),
                    VideoCamId = table.Column<int>(type: "INTEGER", nullable: false),
                    IsCompleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsSuccessed = table.Column<bool>(type: "INTEGER", nullable: false),
                    RecordDurationSeconds = table.Column<int>(type: "INTEGER", nullable: false),
                    RecordUrl = table.Column<string>(type: "TEXT", nullable: false),
                    BaUrl = table.Column<string>(type: "TEXT", nullable: false),
                    SendNotification = table.Column<bool>(type: "INTEGER", nullable: false),
                    PublicVideoUrl = table.Column<string>(type: "TEXT", nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", nullable: false),
                    SmsReceivers = table.Column<string>(type: "TEXT", nullable: false),
                    EmailReceivers = table.Column<string>(type: "TEXT", nullable: false),
                    IsNotificationCompleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsNotificationSuccessed = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RecordingRequests", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RecordingRequests");

            migrationBuilder.CreateTable(
                name: "HiSharpRecordingRequests",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    BaUrl = table.Column<string>(type: "TEXT", nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", nullable: false),
                    EmailReceivers = table.Column<string>(type: "TEXT", nullable: false),
                    IsCompleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsNotificationCompleted = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsNotificationSuccessed = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsSuccessed = table.Column<bool>(type: "INTEGER", nullable: false),
                    PublicVideoUrl = table.Column<string>(type: "TEXT", nullable: false),
                    RecordDurationSeconds = table.Column<int>(type: "INTEGER", nullable: false),
                    RecordUrl = table.Column<string>(type: "TEXT", nullable: false),
                    SendNotification = table.Column<bool>(type: "INTEGER", nullable: false),
                    SmsReceivers = table.Column<string>(type: "TEXT", nullable: false),
                    Time = table.Column<DateTime>(type: "TEXT", nullable: false),
                    VideoCamId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HiSharpRecordingRequests", x => x.Id);
                });
        }
    }
}
