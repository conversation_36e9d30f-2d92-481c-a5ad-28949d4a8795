﻿using Microsoft.Extensions.Options;
using System.Text.Json.Serialization;
using System.Web;

namespace TwentyFourDioGateway.SafetyMsg
{
    public class HundureCard
    {
        [JsonPropertyName("card_id")]
        public string CardId { get; set; } = string.Empty;
        [JsonPropertyName("door_id")]
        public string DoorId { get; set; } = string.Empty;
        [JsonPropertyName("cam_id")]
        public string CamIp { get; set; } = string.Empty;
        [JsonPropertyName("display_name")]
        public string DisplayName { get; set; } = string.Empty;
        [JsonPropertyName("email_receivers")]
        public string EmailReceivers { get; set; } = string.Empty;
        [JsonPropertyName("sms_receivers")]
        public string SmsReceivers { get; set; } = string.Empty;
    }
    public class HundureCardListService
    {
        private readonly DeviceMapping _deviceMapping;
        private readonly IHttpClientFactory _httpClientFactory;
        public HundureCardListService(
            IOptions<DeviceMapping> deviceMapping,
            IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
            if (deviceMapping != null)
            {
                _deviceMapping = deviceMapping.Value;
            }
        }
        
        public async Task<HundureCard?> GetHundureCardAsync(string cardId, string doorId)
        {
            var url = $"{_deviceMapping.BaUrl}/index.php?option=com_floor&task=sroots.get_card_info&card_id={HttpUtility.UrlEncode(cardId)}&door_id={HttpUtility.UrlEncode(doorId)}";
            var result = await _httpClientFactory.CreateClient().GetAsync(url);
            result.EnsureSuccessStatusCode();
            var cc = await result.Content.ReadAsStringAsync();
            var content = await result.Content.ReadFromJsonAsync<List<HundureCard>>();
            if (content is null || content.Count == 0)
            {
                return null;
            }
            return content.First();
        }
        /// <summary>
        /// if card exists, return camId, else return -1
        /// </summary>
        /// <param name="cardId"></param>
        /// <param name="doorId"></param>
        /// <returns></returns>
        public int GetCamId(HundureCard card)
        {
            //var card = Cards.FirstOrDefault(c => c.CardId == cardId && c.DoorId == doorId);
            //if (card == null)
            //{
            //    return -1;
            //}
            var cam = _deviceMapping.VideoCameras.FirstOrDefault(c => c.Ip == card.CamIp);
            if (cam != null)
            {
                return cam.Id;
            }
            return -1;
        }
    }
}
