using System.Collections.Concurrent;

namespace TwentyFourDioGateway;

/// <summary>
/// 空調指令佇列管理器
/// 實現優先權佇列，啟停指令可以插隊
/// </summary>
public class AirConditionerCommandQueue
{
    private readonly PriorityQueue<AirConditionerCommand, int> _commandQueue = new();
    private readonly SemaphoreSlim _queueSemaphore = new(1, 1);
    private readonly SemaphoreSlim _executionSemaphore = new(1, 1);
    private readonly ILogger<AirConditionerCommandQueue> _logger;
    private readonly string _groupName;
    private bool _disposed = false;

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="groupName">群組名稱</param>
    /// <param name="logger">日誌記錄器</param>
    public AirConditionerCommandQueue(string groupName, ILogger<AirConditionerCommandQueue> logger)
    {
        _groupName = groupName;
        _logger = logger;
    }

    /// <summary>
    /// 加入指令到佇列
    /// </summary>
    /// <param name="command">指令</param>
    /// <returns>是否成功加入</returns>
    public async Task<bool> EnqueueAsync(AirConditionerCommand command)
    {
        if (_disposed)
            return false;

        try
        {
            await _queueSemaphore.WaitAsync();
            try
            {
                // 使用負數作為優先權，因為 PriorityQueue 是最小堆積
                // 控制指令優先權 = -1，查詢指令優先權 = 0
                var priority = -command.Priority;
                _commandQueue.Enqueue(command, priority);

                _logger.LogDebug("Command enqueued: Group={GroupName}, Type={CommandType}, AirConditionerId={AirConditionerId}, QueueSize={QueueSize}",
                    _groupName, command.CommandType, command.AirConditionerId, _commandQueue.Count);

                return true;
            }
            finally
            {
                _queueSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to enqueue command: Group={GroupName}, Type={CommandType}, AirConditionerId={AirConditionerId}",
                _groupName, command.CommandType, command.AirConditionerId);
            return false;
        }
    }

    /// <summary>
    /// 從佇列取出下一個指令
    /// </summary>
    /// <returns>下一個指令，如果佇列為空則返回 null</returns>
    public async Task<AirConditionerCommand?> DequeueAsync()
    {
        if (_disposed)
            return null;

        try
        {
            await _queueSemaphore.WaitAsync();
            try
            {
                if (_commandQueue.Count > 0)
                {
                    var command = _commandQueue.Dequeue();
                    _logger.LogDebug("Command dequeued: Group={GroupName}, Type={CommandType}, AirConditionerId={AirConditionerId}, RemainingQueueSize={QueueSize}",
                        _groupName, command.CommandType, command.AirConditionerId, _commandQueue.Count);
                    return command;
                }
                return null;
            }
            finally
            {
                _queueSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dequeue command: Group={GroupName}", _groupName);
            return null;
        }
    }

    /// <summary>
    /// 取得佇列大小
    /// </summary>
    /// <returns>佇列中的指令數量</returns>
    public async Task<int> GetQueueSizeAsync()
    {
        if (_disposed)
            return 0;

        try
        {
            await _queueSemaphore.WaitAsync();
            try
            {
                return _commandQueue.Count;
            }
            finally
            {
                _queueSemaphore.Release();
            }
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 等待執行權限
    /// </summary>
    /// <param name="timeout">超時時間</param>
    /// <returns>是否取得執行權限</returns>
    public async Task<bool> WaitForExecutionAsync(TimeSpan timeout)
    {
        if (_disposed)
            return false;

        try
        {
            return await _executionSemaphore.WaitAsync(timeout);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 釋放執行權限
    /// </summary>
    public void ReleaseExecution()
    {
        try
        {
            _executionSemaphore.Release();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to release execution semaphore: Group={GroupName}", _groupName);
        }
    }

    /// <summary>
    /// 清空佇列
    /// </summary>
    public async Task ClearAsync()
    {
        if (_disposed)
            return;

        try
        {
            await _queueSemaphore.WaitAsync();
            try
            {
                var cancelledCount = 0;
                while (_commandQueue.Count > 0)
                {
                    var command = _commandQueue.Dequeue();
                    command.TaskCompletionSource.SetCanceled();
                    cancelledCount++;
                }

                if (cancelledCount > 0)
                {
                    _logger.LogInformation("Cleared command queue: Group={GroupName}, CancelledCommands={CancelledCount}",
                        _groupName, cancelledCount);
                }
            }
            finally
            {
                _queueSemaphore.Release();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear command queue: Group={GroupName}", _groupName);
        }
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
            
            // 清空佇列並取消所有等待的任務
            _ = Task.Run(async () => await ClearAsync());
            
            _queueSemaphore.Dispose();
            _executionSemaphore.Dispose();
        }
    }
}
