﻿using TwentyFourDioGateway;
using System.Net;
using System.Net.Sockets;
using System.Collections.Concurrent;
using Microsoft.Extensions.DependencyModel;
using IoC;

namespace TwentyFourDioGateway
{

    //public interface ITwentyFourDioPollingEngineFactory
    //{
    //    TwentyFourDioPollingEngine Create(string id, IPEndPoint iPEndPoint, CancellationToken Token);
    //}
    //public class TwentyFourDioPollingEngineFactory : ITwentyFourDioPollingEngineFactory
    //{
    //    private readonly IDependency _dependency;

    //    public TwentyFourDioPollingEngineFactory(IDependency dependency)
    //    {
    //        _dependency = dependency;
    //    }

    //    public TwentyFourDioPollingEngine Create(string id, IPEndPoint iPEndPoint, CancellationToken Token)
    //    {
    //        return new TwentyFourDioPollingEngine(_dependency, id, iPEndPoint, Token);
    //    }

    //}
    public class TwentyFourDioPollingEngine
    {

        private IPEndPoint _endpoint { get; set; }
        private TwentyFourDioMaster _twentyFourDioMaster { get; set; }
        public Func<IEnumerable<bool>, Task>? NewDiValuesCallBack { get; set; }
        public Func<IEnumerable<bool>, Task>? NewDoValuesCallBack { get; set; }
        public  ConcurrentQueue<(byte Index, bool Value)?> CommandQueue = new ConcurrentQueue<(byte Index, bool Value)?>();
        private ILogger<TwentyFourDioPollingEngine> logger;
        private ILogger<TwentyFourDioMaster> logger1;
        public string Id;
        private readonly CancellationToken _token;
        public TwentyFourDioPollingEngine(
            ILogger<TwentyFourDioPollingEngine> logger, 
            ILogger<TwentyFourDioMaster> logger1,
            string id,
            IPEndPoint iPEndPoint, 
            CancellationToken Token)
        {
            this.logger = logger;
            this.logger1 = logger1;
            //_dependency = dependency;
            Id = id;
            _endpoint = iPEndPoint;
            _twentyFourDioMaster = new TwentyFourDioMaster(logger1,id, iPEndPoint, Token);
            _token = Token;
        }

        public async Task Poll()
        {
            while (true)
            {
                try
                {
                    await _twentyFourDioMaster.ConnectAsync();

                    while (true)
                    {


                        _twentyFourDioMaster.DataReceivedCallBack = async (string response) =>
                        {

                            //Console.WriteLine($"Received: {response}");
                        };
                        _twentyFourDioMaster.DataChangedCallBack = async (string response) =>
                        {
                            var logString = string.Empty;
                            if (response.StartsWith("@DI"))
                            {
                                if (NewDiValuesCallBack != null)
                                {
                                    await NewDiValuesCallBack.Invoke(response.Skip(4).Take(24).Select(e => e == '1'));
                                }
                                logString = response.Insert(8, "_").Insert(13, "_").Insert(18, "_").Insert(23, "_").Insert(28, "_");
                            }
                            else if (response.StartsWith("@DO"))
                            {
                                if (NewDoValuesCallBack != null)
                                {
                                    await NewDoValuesCallBack.Invoke(response.Skip(4).Take(8).Select(e => e == '1'));
                                }
                                logString = response.Insert(8, "_");
                            }
                            logger.LogInformation($"{_endpoint.Address}@{_endpoint.Port} Changed: {logString}");
                        };


                        while (true)
                        {
                            if (!_twentyFourDioMaster.IsConnected)
                            {

                                for (int i = 0; i < 15; i++)
                                {
                                    if (_token.IsCancellationRequested)
                                    {
                                        return;
                                    }
                                    await Task.Delay(1_000);
                                }
                                if (await _twentyFourDioMaster.ConnectAsync())
                                {
                                    break;
                                }
                                //_twentyFourDioMaster.Disconnect();
                                for (int i = 0; i < 15; i++)
                                {
                                    if (_token.IsCancellationRequested)
                                    {
                                        return;
                                    }
                                    await Task.Delay(1_000);
                                }
                            }
                            //CommandQueue.TryPeek(out var item);
                            //Console.WriteLine(item);

                            try
                            {
                                if (CommandQueue.TryPeek(out var peek))
                                {
                                    while (CommandQueue.TryDequeue(out var item))
                                    {
                                        //Console.WriteLine("Dequeued");
                                        //Console.WriteLine($"Dequeued, Polling {Id}");
                                        _twentyFourDioMaster.CommandQueue.Enqueue(item);
                                        item = null;
                                    }
                                }
                            
                            }
                            catch (Exception e)
                            {
                                logger.LogError(e, $"{_endpoint.Address}@{_endpoint.Port} 24Engine: {e}");
                            }
                

                            if (_token.IsCancellationRequested)
                            {
                                return;
                            }
                            await Task.Delay(10);
                        }
                    }
                }
                catch (SocketException e)
                {
                    //Console.WriteLine($"{DateTime.Now.ToString("dd HH:mm:ss fff")} 24Engine: {_endpoint.Address} Socket Exception");
                    for (int i = 0; i < 15; i++)
                    {
                        if (_token.IsCancellationRequested)
                        {
                            return;
                        }
                        await Task.Delay(1_000);
                    }
                }
                catch (Exception e)
                {
                    logger.LogError(e,$"{_endpoint.Address}@{_endpoint.Port} 24Engine: {e}");
                }

            }

        }
    }
}
