﻿using Microsoft.Extensions.Options;

namespace TwentyFourDioGateway;

public class DeviceMapping
{
    public List<ushort> BindDoWithDiAddresses { get; set; } = new List<ushort>();
    public string HiSharpDefaultUsername { get; set; } = string.Empty;
    public string HiSharpDefaultPassword { get; set; } = string.Empty;
    public string FuhoDefaultUsername { get; set; } = string.Empty;
    public string FuhoDefaultPassword { get; set; } = string.Empty;
    public List<TwentyFourDioDevice> Devices { get; set; } = new List<TwentyFourDioDevice>();
    public string PublicVideoUrlPrefix { get; set; } = string.Empty;
    public int RecordTimeoutSeconds { get; set; } = 300;
    public string RecordPath { get; set; } = @"C:\Weema\records";

    /// <summary>
    /// DO轉AO群組配置列表
    /// </summary>
    public List<DoMapAoGroup> DoMapAoGroups { get; set; } = new List<DoMapAoGroup>();
    public string BaUrl { get; set; } = string.Empty;
    public int RecordWaitSeconds { get; set; } = 60;
    public string HundureServerAddress { get; set; } = string.Empty;
    public List<HundureCardReaderEndpoint> HundureCardReaderEndpoints { get; set; } = new List<HundureCardReaderEndpoint>();
    public int HundureIllegalCardElapseSeconds { get; set; } = 30;
    public List<HiSharpAiCamera> HiSharpAiCameras { get; set; } = new List<HiSharpAiCamera>();
    public int HiSharpAiCameraAlarmTimeoutSeconds { get; set; } = 30;
    public bool HiSharpUsePlaybackMode { get; set;} = false;
    public bool HiSharpUseVlcRecordingMode { get; set; } = false;
    public List<VideoCamera> VideoCameras { get; set; } = new List<VideoCamera>();
    public List<FuhoMotionDetectionCamera> FuhoMotionDetectionCameras { get; set; } = new List<FuhoMotionDetectionCamera>();
    public int FuhoMotionDetectionAlarmTimeoutSeconds { get; set; } = 30;
    public int PingTimeout { get; set; } = 1000;
    public int PingBatchSize { get; set; } = 300;
    public int PingDeadMinutes { get; set; } = 1;
    public int PingRetryAttempts { get; set; } = 3;
    public List<IpEndpoint> PingIps { get; set; } = new List<IpEndpoint>();

    // CwbWeather Configuration
    public string CwbWeatherApiKey { get; set; } = string.Empty;
    public string CwbWeatherLocation { get; set; } = string.Empty;
    public string CwbWeatherLocationName { get; set; } = string.Empty;
    public int CwbWeatherUpdateIntervalMinutes { get; set; } = 1;

    // Asterisk 分機配置
    public List<AsteriskExtension> AsteriskExtensions { get; set; } = new List<AsteriskExtension>();

    /// <summary>
    /// 空調控制群組配置列表
    /// </summary>
    public List<KingmanAirGroup> KingmanAirGroups { get; set; } = new List<KingmanAirGroup>();
}

// Asterisk 分機映射類別
public class AsteriskExtension
{
    /// <summary>
    /// 起始分機號碼
    /// </summary>
    public string StartExtensionNumber { get; set; } = string.Empty;

    /// <summary>
    /// 結束分機號碼
    /// </summary>
    public string EndExtensionNumber { get; set; } = string.Empty;

    /// <summary>
    /// 起始分機在線狀態的 Modbus 位址
    /// </summary>
    public ushort StartOnlineStatusModbusAddress { get; set; }

    /// <summary>
    /// 起始分機通話狀態的 Modbus 位址
    /// </summary>
    public ushort StartCallStatusModbusAddress { get; set; }

    /// <summary>
    /// 單一分機設定 (保留向下相容性)
    /// </summary>
    public string? Extension { get; set; }

    /// <summary>
    /// 單一分機在線狀態的 Modbus 位址 (保留向下相容性)
    /// </summary>
    public ushort? OnlineStatusModbusAddress { get; set; }

    /// <summary>
    /// 單一分機通話狀態的 Modbus 位址 (保留向下相容性)
    /// </summary>
    public ushort? CallStatusModbusAddress { get; set; }
}

public class HiSharpAiCamera
{
    public string Ip { get; set; } = string.Empty;
    public ushort Port { get; set; } = 8080;
    public string Username { get; set; } = "admin";
    public string Password { get; set; } = "123456";
    public ushort ModbusAddress { get; set; }
}
public class HundureCardReaderEndpoint
{
    public string DoorName { get; set; } = string.Empty;
    public ushort IllegalCardModbusAddress { get; set; }
}
public class IpEndpoint
{
    public string StartAddress { get; set; } = string.Empty;
    public byte Length { get; set; }
    public ushort ModbusAddress { get; set; }
}
public class FuhoMotionDetectionCamera
{
    public string Id { get; set; } = string.Empty;
    public ushort ModbusAddress { get; set; }
    public int AlarmTimeoutSeconds { get; set; }
}
public class VideoCamera
{
    public string Brand { get; set; } = string.Empty;
    public int Id { get; set; }
    public string Ip { get; set; } = string.Empty;
    public ushort? Port { get; set; } = 80;
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}
public class TwentyFourDioDevice
{
    public string Ip { get; set; } = string.Empty;
    public ushort Port { get; set; }
    public ushort DiStartAddress { get; set; }
    public ushort DoStartAddress { get; set; }
    public bool IsOrtisElevator { get; set; } = false;
    public ushort OrtisElevatorMapAddress1 { get; set; } = 1;
    public byte OrtisElevatorStartBit1 { get; set; } = 1;
    public ushort OrtisElevatorMapAddress2 { get; set; } = 2;
    public byte OrtisElevatorStartBit2 { get; set; } = 7;
    public string Id { get => $"{Ip.ToUpper()}@{Port.ToString().ToUpper()}"; }
}

/// <summary>
/// DO轉AO群組配置
/// </summary>
public class DoMapAoGroup
{
    /// <summary>
    /// 群組名稱
    /// </summary>
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// 遠端ModbusTCP Slave的IP位址
    /// </summary>
    public string RemoteIp { get; set; } = string.Empty;

    /// <summary>
    /// 遠端ModbusTCP Slave的Port
    /// </summary>
    public ushort RemotePort { get; set; } = 502;

    /// <summary>
    /// 遠端ModbusTCP Slave的ID
    /// </summary>
    public byte RemoteSlaveId { get; set; } = 1;

    /// <summary>
    /// 單次輪詢的最大 Holding Register 數量 (Modbus Function Code 03)
    /// 預設值為 125，這是 Modbus 協議的最大建議值
    /// </summary>
    public ushort MaxPollingQuantity { get; set; } = 16;

    /// <summary>
    /// DO到AO的映射配置列表
    /// </summary>
    public List<DoToAoMapping> Mappings { get; set; } = new List<DoToAoMapping>();
}

/// <summary>
/// DO到AO的映射配置
/// </summary>
public class DoToAoMapping
{
    /// <summary>
    /// 本地DO位址
    /// </summary>
    public ushort LocalDoAddress { get; set; }

    /// <summary>
    /// 遠端Holding Register位址
    /// </summary>
    public ushort RemoteHoldingRegisterAddress { get; set; }

    /// <summary>
    /// 當DO為True時寫入的數值
    /// </summary>
    public ushort ValueWhenTrue { get; set; } = 1;

    /// <summary>
    /// 當DO為False時寫入的數值
    /// </summary>
    public ushort ValueWhenFalse { get; set; } = 0;
}

/// <summary>
/// 空調控制群組配置
/// </summary>
public class KingmanAirGroup
{
    /// <summary>
    /// 群組名稱
    /// </summary>
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// 遠端空調控制器的IP位址
    /// </summary>
    public string RemoteIp { get; set; } = string.Empty;

    /// <summary>
    /// 遠端空調控制器的Port
    /// </summary>
    public ushort RemotePort { get; set; } = 502;

    /// <summary>
    /// 空調機號到DI/DO位址的映射配置列表
    /// </summary>
    public List<KingmanAirMapping> Mappings { get; set; } = new List<KingmanAirMapping>();
}

/// <summary>
/// 空調機號到DI/DO位址的映射配置
/// </summary>
public class KingmanAirMapping
{
    /// <summary>
    /// 空調機號 (1-255)
    /// </summary>
    public byte AirConditionerId { get; set; }

    /// <summary>
    /// DI位址 - 顯示空調開關機狀態 (0=關機, 1=開機)
    /// </summary>
    public ushort DiAddress { get; set; }

    /// <summary>
    /// DO位址 - 控制空調開關機 (寫入1=開機, 寫入0=關機)
    /// </summary>
    public ushort DoAddress { get; set; }
}