﻿
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace TwentyFourDioGateway.HiSharpAi
{

    public class XmlExtractor
    {
        /// <summary>
        /// 從輸入的字串中提取從 <?xml 開頭到 </config> 結尾的 XML 部分。
        /// </summary>
        /// <param name="input">包含 XML 的完整字串。</param>
        /// <returns>提取出的 XML 字串，如果未找到則返回空字串。</returns>
        public static string ExtractXml(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            string startTag = "<?xml";
            string endTag = "</config>";

            // 查找 <?xml 的起始位置
            int startIndex = input.IndexOf(startTag, StringComparison.Ordinal);
            if (startIndex == -1)
                return string.Empty; // 未找到 <?xml

            // 查找 </config> 的結束位置
            int endIndex = input.IndexOf(endTag, startIndex, StringComparison.Ordinal);
            if (endIndex == -1)
                return string.Empty; // 未找到 </config>

            // 計算提取的長度，包含 </config> 的結尾
            int length = endIndex + endTag.Length - startIndex;

            // 提取子字串
            return input.Substring(startIndex, length);
        }
    }
    public class HISharpAiCameraBackgroundService : BackgroundService
    {

        private readonly IServiceProvider serviceProvider;
        private readonly ILogger<HISharpAiCameraBackgroundService> logger;
        private readonly GatewayService gatewayService;
        private readonly DeviceMapping deviceMapping;
        private readonly Setting setting;
        private readonly ConcurrentDictionary<ushort, DateTime> lastUpdateTimes = new ();
        private List<Task> _tasks = new List<Task>();
        public HISharpAiCameraBackgroundService(
            IServiceProvider serviceProvider,
            IOptions<Setting> setting,
            IOptions<DeviceMapping> deviceMapping,
            ILogger<HISharpAiCameraBackgroundService> logger,
            GatewayService gatewayService
            )
        {
            this.setting = setting.Value;
            this.serviceProvider = serviceProvider;
            this.logger = logger;
            this.gatewayService = gatewayService;
            this.deviceMapping = deviceMapping.Value;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            
            try
            {
                _tasks.Add(AutoRestoreAsync(stoppingToken));
                var cameras = deviceMapping.HiSharpAiCameras;
                foreach (var camera in cameras)
                {
                    lastUpdateTimes[camera.ModbusAddress] = DateTime.MinValue;
                    _tasks.Add(PoolAsync(camera, stoppingToken));
                }
            }
            catch (Exception)
            {

                //throw;
            }
            await Task.WhenAll(_tasks);
        }
        private async Task AutoRestoreAsync(CancellationToken token)
        {
            while (!token.IsCancellationRequested)
            {
                try
                {
                    foreach (var camera in deviceMapping.HiSharpAiCameras)
                    {
                        if (gatewayService.dataStore.InputDiscretes[camera.ModbusAddress] && lastUpdateTimes.TryGetValue(camera.ModbusAddress, out var lastUpdateTime))
                        {
                            if ((DateTime.Now - lastUpdateTime).TotalSeconds > deviceMapping.HiSharpAiCameraAlarmTimeoutSeconds)
                            {
                                await gatewayService.UpdateStatusAsync(Models.ModbusAddressType.DiscreteInput, camera.ModbusAddress, false);
                                lastUpdateTimes[camera.ModbusAddress] = DateTime.Now;
                                logger.LogInformation($"Hisharp AI camera {camera.Ip}:{camera.Port} {camera.ModbusAddress} triggers timeout, reset status");
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    //throw;
                }
                await Task.Delay(1000, token);
            }
        }
        private bool FindXmlNode(string xmlString, string name)
        {
            try
            {
                var xString = XmlExtractor.ExtractXml(xmlString);
                XDocument doc = XDocument.Parse(xString);
                XNamespace ns = "http://www.ipc.com/ver10";
                var smartTypeElement = doc.Descendants(ns + "smartType")
                                    .FirstOrDefault(e => (string)e.Attribute("type") == "openAlramObj");

                if (smartTypeElement != null)
                {
                    return smartTypeElement!.Value == name;
                }
            }
            catch (Exception)
            {
                
            }
            return false;

            //var xdoc = XDocument.Parse(xmlString);
            //XmlNode node = xdoc.SelectSingleNode("//smartType[@type='openAlramObj']");

        }
        private async Task PoolAsync(HiSharpAiCamera camera, CancellationToken token)
        {
            string endpoint = "/SetSubscribe"; // 目標端點
            string contentType = "application/xml"; // 內容類型，設定為 XML

            // XML 檔案路徑
            string xmlFilePath = "SetSubscribe_req.xml"; // 假設檔案與執行檔在同一目錄

            // 重新連線相關設定
            int maxRetryAttempts = 5; // 最大重試次數
            int retryDelayMilliseconds = 5000; // 每次重試之間的延遲（毫秒）
            int delayBetweenRequestsMilliseconds = 10000; // 每次請求之間的延遲（毫秒）

            // 讀取 XML 檔案內容
            string postData;
            try
            {
                if (!File.Exists(xmlFilePath))
                {
                    logger.LogError($"錯誤：找不到檔案 {xmlFilePath}");
                    return;
                }

                postData = File.ReadAllText(xmlFilePath, Encoding.UTF8);
                logger.LogInformation("已成功讀取 XML 檔案內容。");
            }
            catch (Exception ex)
            {
                logger.LogError($"讀取 XML 檔案時發生錯誤: {ex.Message}");
                return;
            }

            // 建立 Basic Auth 的授權標頭
            string credentials = $"{camera.Username}:{camera.Password}";
            string base64Credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
            string authorizationHeader = $"Authorization: Basic {base64Credentials}";

            // 構建 HTTP POST 請求模板
            StringBuilder httpRequestTemplate = new StringBuilder();
            httpRequestTemplate.AppendLine($"POST {endpoint} HTTP/1.1");
            httpRequestTemplate.AppendLine($"Host: {camera.Ip}:{camera.Port}"); // 包含端口號
            httpRequestTemplate.AppendLine("Connection: close"); // 使用 close 以便伺服器關閉連接
            httpRequestTemplate.AppendLine($"Content-Type: {contentType}");
            httpRequestTemplate.AppendLine($"Content-Length: {Encoding.UTF8.GetByteCount(postData)}");
            httpRequestTemplate.AppendLine(authorizationHeader);
            httpRequestTemplate.AppendLine(); // 空行分隔標頭和主體
            httpRequestTemplate.Append(postData);

            string requestString = httpRequestTemplate.ToString();
            byte[] requestBytes = Encoding.UTF8.GetBytes(requestString);

            logger.LogInformation("開始進入連線和請求迴圈...");

            while (!token.IsCancellationRequested)
            {
                int attempt = 0;
                bool connected = false;

                // 重試連線迴圈
                while (!connected && !token.IsCancellationRequested)
                {
                    try
                    {
                        attempt++;
                        logger.LogInformation($"嘗試連接第 {attempt} 次...");

                        using (TcpClient client = new TcpClient())
                        {
                            logger.LogInformation($"正在連接到伺服器 {camera.Ip}:{camera.Port}...");
                            await client.ConnectAsync(camera.Ip, camera.Port, token);
                            logger.LogInformation("連接成功！");
                            connected = true; // 成功連接

                            using (NetworkStream stream = client.GetStream())
                            {
                                // 發送 HTTP POST 請求到伺服器
                                logger.LogInformation("發送 HTTP POST 請求...");
                                await stream.WriteAsync(requestBytes, 0, requestBytes.Length, token);
                                logger.LogInformation("請求已發送。");

                                // 接收伺服器的回應
                                byte[] buffer = new byte[4096];
                                int bytesRead = 0;

                                logger.LogInformation("等待伺服器回應...");

                                // 使用一個額外的 StringBuilder 來處理封包組合
                                StringBuilder packetBuilder = new StringBuilder();
                                string endTag = "</config>";
                                //var cts = new CancellationTokenSource(10_000);
                                // 讀取直到流中沒有更多資料
                                while (connected && !token.IsCancellationRequested)
                                {
                                    try
                                    {
                                        while ((bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, new CancellationTokenSource(10_000).Token)) > 0)
                                        {
                                            string chunk = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                                            // 將讀取到的資料加入到 packetBuilder 中
                                            packetBuilder.Append(chunk);

                                            string currentData = packetBuilder.ToString();
                                            int endTagIndex;

                                            // 持續檢查是否包含 </config>
                                            while ((endTagIndex = currentData.IndexOf(endTag, StringComparison.OrdinalIgnoreCase)) != -1)
                                            {
                                                // 提取到 </config> 為止的資料
                                                int extractLength = endTagIndex + endTag.Length;
                                                string completePacket = currentData.Substring(0, extractLength);
                                                if (FindXmlNode(packetBuilder.ToString(), "PEA"))
                                                {
                                                    lastUpdateTimes[camera.ModbusAddress] = DateTime.Now;
                                                    if (!gatewayService.dataStore.InputDiscretes[camera.ModbusAddress])
                                                    {
                                                        logger.LogInformation($"Hisharp AI camera {camera.Ip}:{camera.Port} {camera.ModbusAddress} triggers alarm");
                                                        await gatewayService.UpdateStatusAsync(Models.ModbusAddressType.DiscreteInput, camera.ModbusAddress, true);
                                                    }
                                                }
                                                // 輸出完整的封包
                                                //logger.LogInformation("收到一組完整的封包:");
                                                //logger.LogInformation(completePacket);
                                                //logger.LogInformation("----------------------------------------");

                                                // 移除已處理的部分，保留剩餘的資料
                                                currentData = currentData.Substring(extractLength);
                                                packetBuilder.Clear();
                                                packetBuilder.Append(currentData);
                                            }
                                        }

                                    }
                                    catch (OperationCanceledException)
                                    {
                                        // 發送 HTTP POST 請求到伺服器
                                        logger.LogDebug("發送 HTTP POST 請求...");
                                        await stream.WriteAsync(requestBytes, 0, requestBytes.Length, token);
                                        logger.LogDebug("請求已發送。");
                                        continue;
                                    }
                                }
                               

                                // 處理可能剩下的資料（如果沒有以 </config> 結尾）
                                if (packetBuilder.Length > 0)
                                {
                                    logger.LogInformation("收到未完整的封包:");
                                    logger.LogInformation(packetBuilder.ToString());
                                }
                               
                                logger.LogInformation("伺服器回應接收完畢。");
                            }
                        }
                    }
    
                    catch (SocketException se)
                    {
                        logger.LogInformation($"SocketException: {se.Message}");

                        //if (attempt >= maxRetryAttempts)
                        //{
                        //    logger.LogWarning("已達到最大重試次數，將進行下一輪嘗試。");
                        //    break;
                        //}

                        logger.LogInformation($"等待 {retryDelayMilliseconds / 1000} 秒後重試...");
                        await Task.Delay(retryDelayMilliseconds, token);
                    }
                    catch (IOException ioe)
                    {
                        logger.LogInformation($"IOException: {ioe.Message}");

                        //if (attempt >= maxRetryAttempts)
                        //{
                        //    logger.LogWarning("已達到最大重試次數，將進行下一輪嘗試。");
                        //    break;
                        //}

                        logger.LogInformation($"等待 {retryDelayMilliseconds / 1000} 秒後重試...");
                        await Task.Delay(retryDelayMilliseconds, token);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"Exception: {ex.Message}");
                        logger.LogError("發生未預期的錯誤，將進行下一輪嘗試。");
                        break;
                    }
                }

                if (!connected)
                {
                    logger.LogWarning("無法連接到伺服器，將等待後重新嘗試。");
                }

                logger.LogInformation($"等待 {delayBetweenRequestsMilliseconds / 1000} 秒後重新連線並發送請求...");
                await Task.Delay(delayBetweenRequestsMilliseconds, token);
            }
        }
    }
}
