﻿namespace TwentyFourDioGateway.Models
{
    public class DawuHistory
    {
        public UInt64 Id { get; set; }
        public DateTime DateTime { get; set; } = DateTime.Now;
        public string Body { get; set; } = string.Empty;
        //public string Response { get; set; } = string.Empty;
        //public DateTime? ResponseDateTime { get; set; } 
        public string TwentyFourDioIp { get; set; } = string.Empty;
        public byte TwentyFourDioIndex { get; set; }        
        public byte SubnetChannelId { get; set; }
        //public bool IsResponse { get; set; } = false;
        public bool Status { get; set; }
    }

}
