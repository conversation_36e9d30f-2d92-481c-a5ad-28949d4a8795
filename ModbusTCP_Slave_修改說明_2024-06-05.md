# ModbusTCP Slave 服務穩定性改進說明

**日期：2024-06-05**

## 問題描述

ModbusTCP Slave 服務在運行一段時間後，有時會無法被 ModbusTCP Master 連線，但系統日誌中沒有錯誤訊息，且系統仍然正常與其他設備通訊。

## 問題分析

經過程式碼分析，發現以下可能的問題：

1. **資源洩漏問題**：在 `ModbusTcpServer.cs` 中，`StartListen` 方法啟動了 TcpListener 並創建了 ModbusTcpSlave，但沒有適當的錯誤處理和資源釋放機制。

2. **執行緒同步問題**：在 `GatewayService.cs` 的 `ModbusServer_valuesChanged` 方法中，使用了 `Task.Run(...).Wait()`，這可能導致死鎖或競爭條件。

3. **連線監控機制缺失**：系統沒有監控 ModbusTCP slave 服務的健康狀態，無法檢測到連線問題並自動重啟服務。

4. **NModbus4.Core 版本問題**：專案使用的是 NModbus4.Core 1.0.2 版本，這可能存在一些已知的連線穩定性問題。

## 改進措施

### 1. 改進 ModbusTcpServer 類別

- 增加了錯誤處理和資源釋放機制
- 增加了服務健康監控和自動重啟機制
- 增加了詳細的日誌記錄
- 使用了執行緒同步鎖來避免競爭條件

### 2. 改進 ModbusTcpServerService 類別

- 增加了健康檢查任務
- 改進了錯誤處理
- 增加了詳細的日誌記錄

### 3. 改進 GatewayService 類別中的 ModbusServer_valuesChanged 方法

- 避免使用 `Task.Run(...).Wait()` 來防止死鎖
- 使用非同步處理方式處理請求
- 增加了詳細的日誌記錄
- 改進了錯誤處理

### 4. 增加了程式碼註釋和日誌記錄

- 為所有方法增加了詳細的註釋
- 增加了更多的日誌記錄點，以便更好地診斷問題

## 修改的檔案

1. `ModbusTcpServer.cs`
2. `ModbusTcpServerService.cs`
3. `GatewayService.cs`
4. `Program.cs`

## 預期效果

1. ModbusTCP Slave 服務將更加穩定，不再出現無法連線的問題
2. 如果服務出現問題，系統將自動重啟服務
3. 系統日誌將包含更多有用的診斷信息
4. 避免了可能的死鎖和競爭條件

## 後續建議

1. 考慮升級 NModbus4.Core 庫到最新版本
2. 增加更多的監控指標，例如連線數量、請求處理時間等
3. 考慮實現一個 Web 界面來監控 ModbusTCP 服務的狀態
4. 定期檢查系統日誌，以便及時發現潛在問題
