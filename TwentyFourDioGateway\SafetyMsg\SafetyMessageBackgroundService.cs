﻿using IoC;
using IoC.Features;
using Microsoft.Extensions.Options;
using System.Text.Json;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway.SafetyMsg
{
    public class SafetyMessageBackgroundService : BackgroundService
    {
        private readonly IServiceProvider serviceProvider;
        private readonly Setting settings;
        private readonly DeviceMapping deviceMapping;
        private readonly ILogger<SafetyMessageBackgroundService> logger;
        public SafetyMessageBackgroundService(
            ILogger<SafetyMessageBackgroundService> logger,
            IOptions<DeviceMapping> deviceMapping,
            IOptions<Setting> settings,
            IServiceProvider serviceProvider)

        {
            this.deviceMapping = deviceMapping.Value;
            this.logger = logger;
            this.settings = settings.Value;
            this.serviceProvider = serviceProvider;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {

            //var setting = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IOptions<Setting>>();
            //if (!setting.Value.EnableSafetyMessage)
            //{
            //    return;
            //}
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    DateTime checkTime = DateTime.Now;
                    
                    while (!stoppingToken.IsCancellationRequested)
                    {

                        try
                        {
                            //檢查有沒有新的刷卡紀錄
                            var result = await CheckHundureApi(checkTime);
                            if (result.Any())
                            {
                                //取得最後一筆刷卡紀錄的時間
                                var lastTime = result.OrderByDescending(r => r.EventDateTime).First();

                                //設定下次檢查的時間
                                var nextCheckTime = lastTime.EventDateTime;

                                checkTime = nextCheckTime.AddSeconds(1);

                                //處理合法卡片
                                if (settings.EnableSafetyMessage)
                                {
                                    await HandleLegalCardMessage(result);
                                }
                                //處理非法卡片
                                if (settings.EnableHundureIllegalCardNotification)
                                {
                                    await HandleIllegalCardMessage(result);
                                }

                                //HiSharpVideoRecordingHostedService
                            }
                        }
                        catch (Exception e)
                        {


                        }
                        await Task.Delay(1000, stoppingToken);

                    }
                }
                catch (Exception)
                {

                }
                await Task.Delay(1000, stoppingToken);
            }
            
            
        }
        private async Task HandleLegalCardMessage(List<HundureGetPubEventResponseModelDetail> result)
        {
            result = result.Where(r => r.EventCode_ID == "010001").ToList();
            result.Select(r => r.EventDateTime).ToList().ForEach(d => Console.WriteLine(d));
            Console.WriteLine(result.Count);
            var exists = result;//.Where(r => hundureCardListService.IsCardExists(r.CardNo, r.DoorName)).ToList();
            var hundureCardListService = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<HundureCardListService>();
            var hisharRecordingService = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<VideoRecordingService>();
            var tasks = result.Select(async e =>
            {

                var card = await hundureCardListService.GetHundureCardAsync(e.CardNo, e.DoorName);
                if (card != null)
                {
                    logger.LogInformation($"Safety Message queued: card:{card.CardId}, door:{card.DoorId}");
                    var camId = hundureCardListService.GetCamId(card);
                    await hisharRecordingService.channel.Writer.WriteAsync(new RecordingRequest()
                    {
                        DisplayName = card.DisplayName,
                        SendNotification = true,
                        SmsReceivers = card.SmsReceivers,
                        EmailReceivers = card.EmailReceivers,
                        VideoCamId = camId,
                        Time = e.EventDateTime
                    });
                }
                else
                {
                    logger.LogInformation($"Safety Message queued: card not found");
                }
            });
            await Task.WhenAll(tasks);
        }
        private async Task HandleIllegalCardMessage(List<HundureGetPubEventResponseModelDetail> result)
        {
            result = result.Where(r => r.EventCode_ID == "010007").ToList();
            var gatewayService = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<GatewayService>();
            var tasks = result.Select(async e =>
            {
                var cardReader = deviceMapping.HundureCardReaderEndpoints.FirstOrDefault(c => c.DoorName == e.DoorName);
                if (cardReader is not null)
                {
                    logger.LogInformation($"Illegal card Message updated: card:{e.CardNo}, door:{e.DoorName}");
                    await gatewayService.UpdateStatusAsync(ModbusAddressType.DiscreteInput, cardReader.IllegalCardModbusAddress, true);
                }
            });
            await Task.WhenAll(tasks);
        }
        private async Task<List<HundureGetPubEventResponseModelDetail>> CheckHundureApi(DateTime startTime)
        {
            //var startTime = DateTime.Now.AddMinutes(-30);
            var endTime = DateTime.Now;
            var httpClient = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IHttpClientFactory>().CreateClient();
            var deviceMapping = serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IOptions<DeviceMapping>>();
            var options = new JsonSerializerOptions
            {
                Converters = { new HundureDateTimeJsonConverter() },
                WriteIndented = true
            };
            var command = new HundureGetPubEventRequestCommand
            {
                StartDateTime = startTime,
                EndDateTime = endTime,
                CmdSerial = "1"
            };
            var token = new CancellationTokenSource(2000).Token;
            var result = await httpClient.PostAsJsonAsync($"http://{deviceMapping.Value.HundureServerAddress}/SyncECSService/GetPubEvent", command, options: options, token);
            result.EnsureSuccessStatusCode();
            
            var raw = await result.Content.ReadAsStringAsync();
            var response = await result.Content.ReadFromJsonAsync<HundureGetPubEventResponseModel>(options);
            if (response != null)
            {
                return response.Detail;

                //if (response.EventCode_ID == "010001")
                //{

                //    //search cardno in card list, response.CardNo

                //    //if cardno is in card list, record video and send to the user
                //}
            }
            return [];
        }
       
    }
    public class HundureIllegalCardMessageModel
    {
        public string CardNo { get; set; }
        public string DoorName { get; set; }
        public DateTime EventDateTime { get; set; }
    }
    public class HundureGetPubEventResponseModel
    {
        public string CmdSerial { get; set; }
        public string ErrorCode { get; set; }
        public string ErrorDesc { get; set; }
        public List<HundureGetPubEventResponseModelDetail> Detail { get; set; }
    }
    public class HundureGetPubEventResponseModelDetail
    {
        public string HouseHold { get; set; }
        public string HouseHoldName { get; set; }
        public string Name { get; set; }
        public string CardNo { get; set; }
        public string DoorName { get; set; }
        public DateTime EventDateTime { get; set; }
        public string EventCode_ID { get; set; }
        public string EventCode_Name { get; set; }
    }
    public class HundureGetPubEventRequestCommand
    {
        public string CmdSerial { get; set; } = string.Empty;
        public string HouseHold { get; set; } = string.Empty;
        public string HouseHoldName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string CardNo { get; set; } = string.Empty;
        public string DoorName { get; set; } = string.Empty;
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public string EventCode_ID { get; set; } = string.Empty;
        public string EventCode_Name { get; set; } = string.Empty;
    }
}
