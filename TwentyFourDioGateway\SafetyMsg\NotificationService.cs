﻿using System.Net.Mail;
using System.Net;
using System.Net.Http;
using System.Web;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Channels;
using Microsoft.Extensions.Logging;

namespace TwentyFourDioGateway.SafetyMsg
{
    /// <summary>
    /// 通知服務，提供郵件及簡訊發送功能
    /// </summary>
    public class NotificationService
    {
        private const string EMAIL_SENDER = "<EMAIL>";
        private const string EMAIL_PASSWORD = "ryaqinvkowdqgqhf";
        private const string SMS_USERNAME = "twgtm";
        private const string SMS_PASSWORD = "twgtm";
        private readonly IHttpClientFactory httpClientFactory;
        private readonly Channel<EmailMessage> _emailChannel;
        private readonly ILogger<NotificationService> _logger;

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="httpClientFactory">HTTP客戶端工廠</param>
        /// <param name="emailChannel">郵件佇列Channel</param>
        /// <param name="logger">日誌服務</param>
        public NotificationService(
            IHttpClientFactory httpClientFactory, 
            Channel<EmailMessage> emailChannel,
            ILogger<NotificationService> logger)
        {
            this.httpClientFactory = httpClientFactory;
            _emailChannel = emailChannel;
            _logger = logger;
        }

        private string BuildBody(string displayName, DateTime time, string url)
        {
            return $"{time.ToString("yyyy/MM/dd HH:mm")} {displayName} 已刷卡 {url}";
        }

        /// <summary>
        /// 發送郵件，用於安心簡訊功能
        /// </summary>
        /// <param name="recipient">收件人郵箱地址</param>
        /// <param name="displayName">顯示名稱</param>
        /// <param name="time">時間</param>
        /// <param name="url">URL</param>
        /// <param name="token">取消標記</param>
        /// <returns>異步任務</returns>
        public async Task SendMailAsync(string recipient, string displayName, DateTime time, string url, CancellationToken token)
        {
            await SendMailAsync(recipient, "Weema門禁刷卡通知", BuildBody(displayName, time, url), token);
        }

        /// <summary>
        /// 發送郵件通用方法，允許自訂主旨和內容
        /// </summary>
        /// <param name="recipient">收件人郵箱地址</param>
        /// <param name="subject">郵件主旨</param>
        /// <param name="body">郵件內容</param>
        /// <param name="token">取消標記</param>
        /// <returns>異步任務</returns>
        public async Task SendMailAsync(string recipient, string subject, string body, CancellationToken token)
        {
            // 建立郵件訊息物件並放入佇列
            var emailMessage = new EmailMessage
            {
                Recipient = recipient,
                Subject = subject,
                Body = body,
                CreatedTime = DateTime.Now
            };

            try
            {
                // 將郵件放入佇列
                await _emailChannel.Writer.WriteAsync(emailMessage, token);
                _logger.LogDebug("已將郵件加入佇列，收件人: {Recipient}, 主旨: {Subject}", recipient, subject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "將郵件放入佇列時發生錯誤，收件人: {Recipient}, 主旨: {Subject}", recipient, subject);
                throw;
            }
        }

        /// <summary>
        /// 發送簡訊通知
        /// </summary>
        /// <param name="recipient">收件人手機號碼</param>
        /// <param name="displayName">顯示名稱</param>
        /// <param name="time">時間</param>
        /// <param name="url">URL</param>
        /// <param name="token">取消標記</param>
        /// <returns>異步任務</returns>
        public async Task SendSmsAsync(string recipient, string displayName, DateTime time, string url ,CancellationToken token)
        {
            var client = httpClientFactory.CreateClient();
            var result = await client.GetAsync($"https://api.twsms.com/json/sms_send.php?username={SMS_USERNAME}&password={SMS_PASSWORD}&mobile={recipient}&message={HttpUtility.UrlEncode(BuildBody(displayName, time,url),System.Text.Encoding.UTF8)}", token);
        }
    }
}
