using System.Reflection;

namespace TwentyFourDioGateway.Services;

/// <summary>
/// 版本資訊服務
/// 提供應用程式版本資訊的查詢功能
/// </summary>
public class VersionService
{
    private readonly VersionInfo _versionInfo;

    public VersionService()
    {
        _versionInfo = GetVersionInfo();
    }

    /// <summary>
    /// 取得版本資訊
    /// </summary>
    /// <returns>版本資訊物件</returns>
    public VersionInfo GetVersionInfo()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var assemblyName = assembly.GetName();
        
        // 取得各種版本資訊
        var version = assemblyName.Version?.ToString() ?? "Unknown";
        var fileVersion = assembly.GetCustomAttribute<AssemblyFileVersionAttribute>()?.Version ?? "Unknown";
        var informationalVersion = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion ?? "Unknown";
        var product = assembly.GetCustomAttribute<AssemblyProductAttribute>()?.Product ?? "Unknown";
        var company = assembly.GetCustomAttribute<AssemblyCompanyAttribute>()?.Company ?? "Unknown";
        var copyright = assembly.GetCustomAttribute<AssemblyCopyrightAttribute>()?.Copyright ?? "Unknown";
        var description = assembly.GetCustomAttribute<AssemblyDescriptionAttribute>()?.Description ?? "Unknown";
        
        // 取得建置資訊
        var buildDate = GetBuildDate(assembly);
        var configuration = GetConfiguration();
        
        return new VersionInfo
        {
            Version = version,
            FileVersion = fileVersion,
            InformationalVersion = informationalVersion,
            Product = product,
            Company = company,
            Copyright = copyright,
            Description = description,
            BuildDate = buildDate,
            Configuration = configuration,
            TargetFramework = GetTargetFramework(),
            MachineName = Environment.MachineName,
            UserName = Environment.UserName,
            OSVersion = Environment.OSVersion.ToString(),
            ProcessorCount = Environment.ProcessorCount,
            WorkingSet = Environment.WorkingSet,
            StartTime = DateTime.Now
        };
    }

    /// <summary>
    /// 取得目前的版本資訊
    /// </summary>
    public VersionInfo CurrentVersion => _versionInfo;

    /// <summary>
    /// 取得簡短版本字串
    /// </summary>
    /// <returns>版本字串</returns>
    public string GetVersionString()
    {
        return $"{_versionInfo.Product} v{_versionInfo.Version} ({_versionInfo.Configuration})";
    }

    /// <summary>
    /// 取得詳細版本資訊字串
    /// </summary>
    /// <returns>詳細版本資訊</returns>
    public string GetDetailedVersionString()
    {
        return $"""
            {_versionInfo.Product} v{_versionInfo.Version}
            Build: {_versionInfo.FileVersion} ({_versionInfo.Configuration})
            Built on: {_versionInfo.BuildDate:yyyy-MM-dd HH:mm:ss}
            Framework: {_versionInfo.TargetFramework}
            Company: {_versionInfo.Company}
            Copyright: {_versionInfo.Copyright}
            """;
    }

    /// <summary>
    /// 取得建置日期
    /// </summary>
    private static DateTime GetBuildDate(Assembly assembly)
    {
        try
        {
            // 嘗試從檔案建立時間取得
            var location = assembly.Location;
            if (!string.IsNullOrEmpty(location) && File.Exists(location))
            {
                return File.GetCreationTime(location);
            }
        }
        catch
        {
            // 忽略錯誤
        }
        
        // 如果無法取得檔案時間，使用編譯時間
        return DateTime.Now;
    }

    /// <summary>
    /// 取得建置配置
    /// </summary>
    private static string GetConfiguration()
    {
#if DEBUG
        return "Debug";
#else
        return "Release";
#endif
    }

    /// <summary>
    /// 取得目標框架
    /// </summary>
    private static string GetTargetFramework()
    {
        var framework = Assembly.GetExecutingAssembly()
            .GetCustomAttribute<System.Runtime.Versioning.TargetFrameworkAttribute>()?.FrameworkName;
        
        return framework ?? ".NET Unknown";
    }
}

/// <summary>
/// 版本資訊資料模型
/// </summary>
public class VersionInfo
{
    /// <summary>
    /// 版本號
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 檔案版本號
    /// </summary>
    public string FileVersion { get; set; } = string.Empty;

    /// <summary>
    /// 資訊版本號
    /// </summary>
    public string InformationalVersion { get; set; } = string.Empty;

    /// <summary>
    /// 產品名稱
    /// </summary>
    public string Product { get; set; } = string.Empty;

    /// <summary>
    /// 公司名稱
    /// </summary>
    public string Company { get; set; } = string.Empty;

    /// <summary>
    /// 版權資訊
    /// </summary>
    public string Copyright { get; set; } = string.Empty;

    /// <summary>
    /// 產品描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 建置日期
    /// </summary>
    public DateTime BuildDate { get; set; }

    /// <summary>
    /// 建置配置 (Debug/Release)
    /// </summary>
    public string Configuration { get; set; } = string.Empty;

    /// <summary>
    /// 目標框架
    /// </summary>
    public string TargetFramework { get; set; } = string.Empty;

    /// <summary>
    /// 機器名稱
    /// </summary>
    public string MachineName { get; set; } = string.Empty;

    /// <summary>
    /// 使用者名稱
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 作業系統版本
    /// </summary>
    public string OSVersion { get; set; } = string.Empty;

    /// <summary>
    /// 處理器核心數
    /// </summary>
    public int ProcessorCount { get; set; }

    /// <summary>
    /// 工作集大小 (記憶體使用量)
    /// </summary>
    public long WorkingSet { get; set; }

    /// <summary>
    /// 啟動時間
    /// </summary>
    public DateTime StartTime { get; set; }
}
