using System;

namespace TwentyFourDioGateway
{
    /// <summary>
    /// ModbusTCP 伺服器健康狀態資訊
    /// </summary>
    public class ModbusServerHealthInfo
    {
        /// <summary>
        /// 服務是否正在運行
        /// </summary>
        public bool IsRunning { get; set; }
        
        /// <summary>
        /// 服務啟動時間
        /// </summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>
        /// 最後一次請求時間
        /// </summary>
        public DateTime LastRequestTime { get; set; }
        
        /// <summary>
        /// 總請求數量
        /// </summary>
        public long TotalRequestCount { get; set; }
        
        /// <summary>
        /// 錯誤數量
        /// </summary>
        public long ErrorCount { get; set; }
        
        /// <summary>
        /// 寫入請求數量
        /// </summary>
        public long WriteRequestCount { get; set; }
        
        /// <summary>
        /// 讀取請求數量
        /// </summary>
        public long ReadRequestCount { get; set; }
        
        /// <summary>
        /// 服務運行時間
        /// </summary>
        public TimeSpan Uptime { get; set; }
        
        /// <summary>
        /// 自最後一次請求以來的時間
        /// </summary>
        public TimeSpan TimeSinceLastRequest { get; set; }
    }
}
