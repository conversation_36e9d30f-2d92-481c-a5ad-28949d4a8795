# TwentyFourDioGateway 物聯網閘道器系統

這是一個工業物聯網閘道器系統，整合多種工業設備、安防系統和醫療照護系統，提供統一的Modbus TCP介面。本文檔針對開發者和維護者提供技術細節。

## 系統架構

### 核心元件
- **Modbus TCP Server**: 提供標準Modbus協議接口，允許外部系統與閘道器交互
- **24通道DIO控制**: 管理多個數位輸入輸出設備
- **資料持久層**: SQLite資料庫用於狀態持久化
- **設備整合層**: 整合各類硬體設備的專用介面
- **服務層**: 提供業務邏輯和設備輪詢服務

### 技術堆疊
- .NET 9.0
- Entity Framework Core
- SQLite
- FFmpeg/VLC (用於視頻處理)
- Swagger (API文檔)
- Modbus.Net (Modbus通訊)

## 開發環境配置

### 系統需求
- .NET 7.0 SDK
- SQLite 3
- Windows作業系統
- FFmpeg (視頻處理)
- VLC (選用，特定視頻處理模式)

### 開發設置
1. 克隆專案
2. 確保安裝所有必要依賴
3. 配置`setting.json`和`device_mapping.json`
4. 運行應用程式

## 核心代碼結構

- `Program.cs` - 應用程式入口點，服務配置
- `GatewayService.cs` - 閘道器核心服務
- `TwentyFourDioMaster.cs` - DIO設備主控制器
- `TwentyFourDioPollingEngine.cs` - 設備輪詢引擎
- `TwentyFourDioPollingService.cs` - 輪詢服務管理
- `ModbusTcpServer.cs` - Modbus TCP服務實現
- `VideoRecordingHostedService.cs` - 視頻錄影服務
- `Models/` - 資料模型
- `Services/` - 業務邏輯服務
- `Data/` - 資料存取層
- `Migrations/` - 資料庫遷移

## 主要功能模組

### Modbus TCP服務
- 提供標準Modbus TCP Slave服務
- 支援讀寫Coil和Discrete Input
- 自動狀態同步

### 視頻監控
- 多品牌攝影機支援 (HiSharp, Fuho)
- 事件觸發錄影
- AI事件處理

### 門禁控制
- Hundure門禁系統整合
- 非法卡片警報處理

### 網路監控
- ICMP批量監控
- 設備狀態映射

### 醫療照護
- 大武護理站整合

## 配置說明

系統使用JSON格式的配置文件:

### 主要配置文件

#### 1. setting.json
包含基本系統設置，如Modbus TCP綁定地址、端口和功能開關。

#### 2. device_mapping.json
詳細配置各類設備映射關係，包括:
- DIO設備
- 攝影機
- 門禁系統
- 網路監控IP
- 影片錄製路徑

#### 3. dawu_settings.json (選用)
配置大武護理站相關參數。

## 資料庫結構

系統使用SQLite作為持久化存儲，主要表結構包括:
- 設備狀態表
- 警報記錄表
- 系統日誌表

## API文檔

系統整合了Swagger UI，開發環境下可通過以下方式訪問API文檔：
```
http://[your-host-ip]:[port]/swagger
```

## 技術注意事項

- Modbus地址映射遵循標準Modbus協議
- 設備輪詢間隔可配置，避免過度佔用網路資源
- 視頻錄影使用FFmpeg，需確保命令列參數正確
- 配置修改後需重啟服務以生效
- 建議定期備份SQLite資料庫文件

## 擴展開發指南

添加新設備類型時，需要:
1. 定義設備模型
2. 實現設備通訊介面
3. 在Modbus地址空間分配映射區域
4. 更新輪詢服務以包含新設備

## 常見問題排除

- Modbus通訊問題通常與網路設置或防火牆有關
- 設備無響應時，檢查IP配置和網路連通性
- 資料庫錯誤可能需要檢查訪問權限
- 視頻錄影失敗通常與FFmpeg配置或硬碟空間有關

## 功能特色

- Modbus TCP 伺服器支援，提供設備狀態映射
- Asterisk PBX 分機狀態監控及整合
- 多種協議支援 (HTTP, TCP, Modbus)
- 設備映射與狀態轉換

## 最近更新

- 2025-03-25: 新增 Asterisk 分機區間映射設定功能 [詳細文檔](docs/2025-03-25-1640-asterisk-extensions-range-mapping.md)
- 2025-03-25: 修復 Asterisk API DateTime 格式處理問題 [詳細文檔](docs/2025-03-25-1615-asterisk-datetime-fix.md)
- 2025-03-25: 優化 HttpClient 最佳化設計，避免 Socket 資源耗盡 [詳細文檔](docs/2025-03-25-1618-http-client-optimization.md)
- 2025-03-25: 修復 Asterisk 分機狀態識別問題 [詳細文檔](docs/2025-03-25-1447-changes-summary.md)

## Asterisk 整合

本系統可以與 Asterisk PBX 系統整合，監控分機狀態（在線/離線）及通話狀態。整合功能包括：

1. 透過 ARI (Asterisk REST Interface) 獲取分機狀態
2. 監控分機是否在線
3. 檢測通話活動
4. 將分機狀態映射到 Modbus DI 位址 (Discrete Input)，供其他系統使用
5. **支援分機區間映射**：可以批量設定連續分機區間，簡化配置 (新功能)

配置說明請參考 [Asterisk 整合設定](docs/2025-03-25-1515-asterisk-extension-status-fixes.md)。