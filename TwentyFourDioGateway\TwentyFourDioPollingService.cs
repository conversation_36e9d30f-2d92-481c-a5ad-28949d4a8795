﻿
using TwentyFourDioGateway;
using System.Diagnostics;
using System.Net;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using TwentyFourDioGateway.DawuNursing;

namespace TwentyFourDioGateway;


public class TwentyFourDioPollingService : IHostedService
{
    private List<Task> _tasks = new List<Task>();
    private readonly CancellationTokenSource _cts = new CancellationTokenSource();
    private readonly List<TwentyFourDioDevice> devices;
    private readonly GatewayService gatewayService;
    private readonly ILogger<TwentyFourDioPollingService> logger;
    private readonly ILogger<TwentyFourDioPollingEngine> logger1;
    private readonly ILogger<TwentyFourDioMaster> logger2;
    private List<TwentyFourDioPollingEngine> engines = new List<TwentyFourDioPollingEngine>();
    public TwentyFourDioPollingService(
        ILogger<TwentyFourDioPollingService> logger,
        ILogger<TwentyFourDioPollingEngine> logger1,
        ILogger<TwentyFourDioMaster> logger2,
        IOptions<DeviceMapping> options, GatewayService gatewayService)
    {
        devices = options.Value.Devices;
        this.gatewayService = gatewayService;
        this.logger = logger;
        this.logger1 = logger1;
        this.logger2 = logger2;
    }
    public async Task Polling(CancellationToken token)
    {
        while (true)
        {
            try
            {
                engines = devices.Select(device => new TwentyFourDioPollingEngine(logger1,logger2, device.Id, new IPEndPoint(IPAddress.Parse(device.Ip), device.Port), _cts.Token)
                {
                    NewDoValuesCallBack = async (IEnumerable<bool> result) =>
                    {
                        await gatewayService.UpdateDoStatus(device, result);                        
                        //Console.WriteLine(result);
                    },
                    NewDiValuesCallBack = async (IEnumerable<bool> result) =>
                    {
                        await gatewayService.UpdateDiStatus(device, result);
                        if (device.IsOrtisElevator)
                        {
                            gatewayService.UpdateOrtisElevatorStatus(device, result);
                        }

                        //Console.WriteLine(result);
                    }
                }).ToList();

                List<Task> pollings = new List<Task>();
                foreach (var engine in engines)
                {
                    pollings.Add(engine.Poll());
                }
                
                logger.LogInformation("Task Added");


                while (true)
                {
                    if (gatewayService.commandQueue.TryPeek(out var peeak))
                    {
                        while (gatewayService.commandQueue.TryDequeue(out var item))
                        {
                            var engine = engines.Where(e => e.Id == item.Id).FirstOrDefault();
                            if (engine != null)
                            {
                                //Console.WriteLine($"Dequeued, Polling Service {engine.Id} {engine.GetHashCode()}");

                                engine.CommandQueue.Enqueue((item.Index, item.Value));
                                engine.CommandQueue.TryPeek(out var item1);
                                item1 = null;
                                //Console.WriteLine($"Service : {item1.Index}{item1.Value}");
                            }

                        }
                    }
 
                    if (token.IsCancellationRequested)
                    {
                        logger.LogInformation("TwentyFourDioPollingService Shutdown");
                        return;
                    }

                    await Task.Delay(10);
                }
        
                //await Task.Delay(10);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Polling");
            }

  


        }
    }
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _tasks.Add(Polling(_cts.Token));
        return Task.CompletedTask;
    }
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _cts.Cancel();
        await Task.WhenAll(_tasks);
    }
}