﻿using System.Text.Json.Serialization;

namespace TwentyFourDioGateway.DawuNursing
{
    public class RequestModel
    {
        [JsonIgnore]
        public int SubnetChannelId { get; set; } = 1;
        public string SipNumber { get; set; } = string.Empty;
        public string NurseStation { get; set; } = string.Empty;
        public string Ip { get; set; } = string.Empty;
        public string Mac { get; set; } = string.Empty;
        public byte Action { get; set; } = 0;
        public int CallingType { get; set; } = 700;
        public string DisplayName { get; set; } = string.Empty;
        public long TimeStamp { get; set; } = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds() + 28800;
        public bool ShowImmediately { get; set; } = true;
        public override string ToString()
        {
            return $"Ts: {TimeStamp}, LocalTime:{DateTimeOffset.FromUnixTimeSeconds(TimeStamp-28800).LocalDateTime.ToString("yyyy/MM/dd HH:mm:ss")},  NurseStation: {NurseStation}, SipNumber:{SipNumber}, Action:{Action}";
        }
        public override bool Equals(object? obj)
        {
            var item = obj as RequestModel;
            return item !=null && item.Ip == Ip && item.SipNumber == SipNumber && item.TimeStamp == TimeStamp && item.Action == Action;
        }
    }
}
