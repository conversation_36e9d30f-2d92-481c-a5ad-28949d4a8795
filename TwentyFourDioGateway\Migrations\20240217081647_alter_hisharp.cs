﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    /// <inheritdoc />
    public partial class alter_hisharp : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsSuccessed",
                table: "HiSharpRecordingRequests",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSuccessed",
                table: "HiSharpRecordingRequests");
        }
    }
}
