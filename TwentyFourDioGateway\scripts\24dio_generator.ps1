# 腳本功能: 產生包含設備設定的 JSON 檔案。
# 產生一個陣列，其中包含多個設備的網路和位址設定。
# 每個設備設定包含 IP 位址、通訊埠、 DI 起始位址和 DO 起始位址。
# 您可以透過修改下方的 $startIp 和 $endIp 變數來指定 IP 範圍。
# DI 位址以 10001 為基礎，每個設備偏移 24。
# DO 位址以 10001 為基礎，每個設備偏移 8。
# 輸出檔案為 config.json，編碼為 UTF8 with BOM。

# =================================================================
# || 請在此處設定起始和結束 IP 位址 ||
# =================================================================
$startIp = "*************"
$endIp = "*************"


# 初始化一個空陣列來存放設定物件
$configArray = @()

# 定義基礎位址和偏移量
$baseDiAddress = 10001
$baseDoAddress = 10001
$diOffset = 24
$doOffset = 8
$port = 5801

# 解析 IP 位址以取得網路前綴和起訖八位元數值
try {
    $ipPrefix = $startIp.Substring(0, $startIp.LastIndexOf('.'))
    $startOctet = [int]$startIp.Split('.')[-1]
    $endOctet = [int]$endIp.Split('.')[-1]

    if ($startIp.Substring(0, $startIp.LastIndexOf('.')) -ne $endIp.Substring(0, $endIp.LastIndexOf('.'))) {
        throw "起始和結束 IP 的網段不同！"
    }

    if ($startOctet -gt $endOctet) {
        throw "起始 IP 的最後一個八位元數值大於結束 IP！"
    }
}
catch {
    Write-Error "IP 位址格式錯誤或範圍不正確: $_"
    exit
}

# 迴圈處理指定的 IP 範圍
for ($octet = $startOctet; $octet -le $endOctet; $octet++) {
    $ipAddress = "$ipPrefix.$octet"
    # 計算迴圈索引，用於偏移量計算
    $loopIndex = $octet - $startOctet

    # 計算當前的 DI 和 DO 位址
    $currentDiAddress = $baseDiAddress + ($loopIndex * $diOffset)
    $currentDoAddress = $baseDoAddress + ($loopIndex * $doOffset)

    # 建立一個自訂 PowerShell 物件來存放單一設備的設定
    $configObject = [PSCustomObject]@{
        Ip             = $ipAddress
        Port           = $port
        DiStartAddress = $currentDiAddress
        DoStartAddress = $currentDoAddress
    }

    # 將物件新增到陣列中
    $configArray += $configObject
}

# 將陣列轉換為 JSON 格式，並使用 UTF8 with BOM 編碼寫入檔案
# -Depth 5 確保巢狀物件能正確轉換
$configArray | ConvertTo-Json -Depth 5 | Out-File -FilePath "config.json" -Encoding utf8BOM

Write-Host "成功產生 config.json 檔案。" 