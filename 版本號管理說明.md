# 版本號管理說明

## 📋 版本號機制概述

本專案已實作完整的版本號管理機制，包含：

1. **專案檔案版本設定**
2. **啟動時版本資訊顯示**
3. **Windows 檔案內容版本資訊**
4. **API 端點查詢版本**
5. **詳細的建置資訊**

## 🔧 版本號配置

### 專案檔案設定 (TwentyFourDioGateway.csproj)

```xml
<!-- 版本資訊 -->
<Version>1.2.0</Version>
<AssemblyVersion>*******</AssemblyVersion>
<FileVersion>*******</FileVersion>
<InformationalVersion>1.2.0-$(Configuration)</InformationalVersion>

<!-- 產品資訊 -->
<Product>TwentyFourDio Gateway</Product>
<Company>Weema Technology</Company>
<Copyright>Copyright © Weema Technology 2024</Copyright>
<Description>24DIO Gateway with ModbusTCP, DoToAo, and monitoring services</Description>
```

### 版本號格式說明

- **Version**: 主要版本號 (1.2.0)
- **AssemblyVersion**: 組件版本號 (*******)
- **FileVersion**: 檔案版本號 (*******)
- **InformationalVersion**: 資訊版本號 (1.2.0-Debug/Release)

## 🚀 功能特色

### 1. 啟動時版本顯示

程式啟動時會在控制台顯示：

```
============================================================
TwentyFourDio Gateway v******* (Debug)
============================================================
TwentyFourDio Gateway v*******
Build: ******* (Debug)
Built on: 2024-12-19 15:30:45
Framework: .NET 9.0
Company: Weema Technology
Copyright: Copyright © Weema Technology 2024
============================================================
```

### 2. Windows 檔案內容

在 Windows 中右鍵點擊 .exe 檔案 → 內容 → 詳細資料，可以看到：

- **檔案版本**: *******
- **產品版本**: *******
- **產品名稱**: TwentyFourDio Gateway
- **公司**: Weema Technology
- **版權**: Copyright © Weema Technology 2024
- **檔案描述**: 24DIO Gateway with ModbusTCP, DoToAo, and monitoring services

### 3. API 端點

#### 完整版本資訊
```
GET /api/version
```

回應範例：
```json
{
  "version": "*******",
  "fileVersion": "*******",
  "informationalVersion": "1.2.0-Debug",
  "product": "TwentyFourDio Gateway",
  "company": "Weema Technology",
  "copyright": "Copyright © Weema Technology 2024",
  "description": "24DIO Gateway with ModbusTCP, DoToAo, and monitoring services",
  "buildDate": "2024-12-19T15:30:45.123",
  "configuration": "Debug",
  "targetFramework": ".NET 9.0",
  "machineName": "DESKTOP-ABC123",
  "userName": "weema",
  "osVersion": "Microsoft Windows NT 10.0.22631.0",
  "processorCount": 8,
  "workingSet": 52428800,
  "startTime": "2024-12-19T15:30:45.123"
}
```

#### 簡化版本資訊
```
GET /api/version/simple
```

回應範例：
```json
{
  "version": "*******",
  "product": "TwentyFourDio Gateway",
  "buildDate": "2024-12-19T15:30:45.123",
  "configuration": "Debug"
}
```

## 📝 版本號更新流程

### 1. 更新版本號

修改 `TwentyFourDioGateway.csproj` 中的版本號：

```xml
<Version>1.3.0</Version>
<AssemblyVersion>*******</AssemblyVersion>
<FileVersion>*******</FileVersion>
```

### 2. 版本號命名規則

建議使用 [語意化版本](https://semver.org/lang/zh-TW/) (Semantic Versioning)：

- **主版本號 (Major)**: 不相容的 API 修改
- **次版本號 (Minor)**: 向下相容的功能性新增
- **修訂號 (Patch)**: 向下相容的問題修正

範例：
- `1.0.0` → `1.0.1` (Bug 修正)
- `1.0.1` → `1.1.0` (新功能)
- `1.1.0` → `2.0.0` (重大變更)

### 3. 建置和發布

```bash
# Debug 建置
dotnet build --configuration Debug

# Release 建置
dotnet build --configuration Release

# 發布
dotnet publish --configuration Release --output ./publish
```

## 🔍 版本資訊查詢方式

### 1. 程式啟動時查看控制台
### 2. 查看日誌檔案
### 3. 呼叫 API 端點
### 4. Windows 檔案內容
### 5. 程式內部查詢

```csharp
// 在其他服務中注入 VersionService
public class SomeService
{
    private readonly VersionService _versionService;
    
    public SomeService(VersionService versionService)
    {
        _versionService = versionService;
    }
    
    public void LogVersion()
    {
        var version = _versionService.GetVersionString();
        Console.WriteLine($"Current version: {version}");
    }
}
```

## 📊 版本歷史追蹤

建議在每次發布時記錄：

1. **版本號**
2. **發布日期**
3. **主要變更**
4. **修正的問題**
5. **新增的功能**

這個版本號機制提供了完整的版本管理功能，方便追蹤和管理應用程式的版本資訊。
