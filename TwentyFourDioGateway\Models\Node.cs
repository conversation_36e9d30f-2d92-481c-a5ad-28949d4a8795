﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace TwentyFourDioGateway.Models
{
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum ModbusAddressType
    {
        Coil,
        DiscreteInput,
        HoldingRegister
    }
    public class Node
    {
        public ushort Address { get; set; }
        public ModbusAddressType AddressType { get; set; }
        public bool Value { get; set; }
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
