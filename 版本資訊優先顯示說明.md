# 版本資訊優先顯示說明

## 🎯 實作目標

確保版本資訊在程式啟動時**最優先顯示**，成為第一段 LOG 輸出。

## 🔧 實作方式

### 1. 早期服務註冊
在 Serilog 配置完成後，立即註冊 `VersionService`：

```csharp
// 🚀 優先顯示版本資訊 - 在所有其他初始化之前
// 先註冊 VersionService 以便早期使用
builder.Services.AddSingleton<TwentyFourDioGateway.Services.VersionService>();
```

### 2. 臨時服務提供者
使用臨時的服務提供者來早期取得版本資訊：

```csharp
// 建立臨時的服務提供者來取得版本資訊
using (var tempServiceProvider = builder.Services.BuildServiceProvider())
{
    var tempVersionService = tempServiceProvider.GetRequiredService<TwentyFourDioGateway.Services.VersionService>();
    // ... 顯示版本資訊
}
```

### 3. 優先顯示順序
1. **控制台輸出**：立即在控制台顯示版本資訊
2. **Serilog 記錄**：使用 `Log.Information` 記錄第一條 LOG

## 📋 顯示內容

### 控制台輸出格式
```
================================================================================
🚀 TwentyFourDio Gateway v1.2.0.0 (Debug)
================================================================================
TwentyFourDio Gateway v1.2.0.0
Build: 1.2.0.0 (Debug)
Built on: 2024-12-19 15:30:45
Framework: .NET 9.0
Company: Weema Technology
Copyright: Copyright © Weema Technology 2024
================================================================================
```

### 日誌記錄格式
```
[15:30:45 INF] [Program] 🚀 Application starting: TwentyFourDio Gateway v1.2.0.0 (Debug)
[15:30:45 INF] [Program] 📦 Build info: v1.2.0.0 built on 2024-12-19 15:30:45 (Debug)
[15:30:45 INF] [Program] 🖥️  Runtime info: .NET 9.0 on Microsoft Windows NT 10.0.22631.0
[15:30:45 INF] [Program] 🔧 Application built successfully, starting services...
```

## 🚀 啟動順序

1. **Serilog 配置** → 日誌系統準備就緒
2. **版本資訊顯示** → 🎯 **第一優先**
3. **CORS 配置** → 跨域設定
4. **配置檔案載入** → JSON 配置檔案
5. **服務註冊** → 其他應用程式服務
6. **應用程式建置** → `builder.Build()`
7. **中介軟體配置** → HTTP 管線設定
8. **資料庫初始化** → 資料庫遷移和還原
9. **應用程式啟動** → `app.Run()`

## ✅ 優勢

### 1. 立即可見
- 程式一啟動就能看到版本資訊
- 不需要等待其他服務初始化

### 2. 除錯友善
- 在任何初始化錯誤發生前就能看到版本
- 便於問題追蹤和版本確認

### 3. 日誌完整
- 版本資訊會記錄在所有日誌檔案中
- 成為每次啟動的第一條記錄

### 4. 運維友善
- 快速確認部署的版本是否正確
- 便於版本管理和追蹤

## 🔍 驗證方式

### 1. 控制台輸出
啟動程式後，版本資訊應該是第一個出現的內容。

### 2. 日誌檔案
檢查 `logs/log-*.log` 檔案，版本資訊應該是第一條 INFO 記錄。

### 3. Debug 日誌
檢查 `logs/debug-*.log` 檔案，版本資訊也會記錄在其中。

## 📊 範例輸出

```
================================================================================
🚀 TwentyFourDio Gateway v1.2.0.0 (Debug)
================================================================================
TwentyFourDio Gateway v1.2.0.0
Build: 1.2.0.0 (Debug)
Built on: 2024-12-19 15:30:45
Framework: .NET 9.0
Company: Weema Technology
Copyright: Copyright © Weema Technology 2024
================================================================================

[15:30:45 INF] [Program] 🚀 Application starting: TwentyFourDio Gateway v1.2.0.0 (Debug)
[15:30:45 INF] [Program] 📦 Build info: v1.2.0.0 built on 2024-12-19 15:30:45 (Debug)
[15:30:45 INF] [Program] 🖥️  Runtime info: .NET 9.0 on Microsoft Windows NT 10.0.22631.0
[15:30:45 INF] [Program] 🔧 Application built successfully, starting services...
[15:30:45 INF] [TwentyFourDioGateway.Services.ModbusTcpServerService] ModbusTCP Server starting on port 502
[15:30:45 INF] [TwentyFourDioGateway.Services.DoToAoPollingService] Starting DO to AO polling service...
... 其他服務啟動日誌
```

## 🎯 結論

現在版本資訊會在程式啟動時**最優先顯示**，確保：

1. ✅ 成為第一段控制台輸出
2. ✅ 成為第一條日誌記錄
3. ✅ 包含完整的版本和建置資訊
4. ✅ 使用醒目的格式和 emoji 標示
5. ✅ 在任何錯誤發生前就能看到版本

這樣的設計讓版本資訊真正成為程式啟動的「第一印象」，便於運維和除錯。
