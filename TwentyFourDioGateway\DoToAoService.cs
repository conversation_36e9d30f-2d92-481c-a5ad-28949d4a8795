using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace TwentyFourDioGateway;

/// <summary>
/// Modbus 讀取範圍資訊
/// </summary>
public class ReadRange
{
    /// <summary>
    /// 起始位址
    /// </summary>
    public ushort StartAddress { get; set; }

    /// <summary>
    /// 讀取數量
    /// </summary>
    public ushort Quantity { get; set; }
}

/// <summary>
/// DO轉AO服務，管理DO到AO的映射和ModbusTCP Master連線
/// 支援雙向功能：輪詢讀取遠端Holding Register更新本地DO，以及本地Coil Write寫入遠端Holding Register
/// </summary>
public class DoToAoService : IDisposable
{
    private readonly DeviceMapping _deviceMapping;
    private readonly ILogger<DoToAoService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ConcurrentDictionary<string, ModbusTcpMasterClient> _masterClients;
    private readonly ConcurrentDictionary<string, ushort[]> _lastPolledValues; // 記錄上次輪詢的值
    private bool _disposed = false;

    /// <summary>
    /// DO狀態變更事件，當輪詢發現遠端Holding Register變更時觸發
    /// </summary>
    public event Action<ushort, bool>? DoStateChanged;

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="deviceMappingOptions">設備映射配置</param>
    /// <param name="logger">日誌記錄器</param>
    /// <param name="loggerFactory">日誌工廠</param>
    public DoToAoService(
        IOptions<DeviceMapping> deviceMappingOptions,
        ILogger<DoToAoService> logger,
        ILoggerFactory loggerFactory)
    {
        _deviceMapping = deviceMappingOptions.Value;
        _logger = logger;
        _loggerFactory = loggerFactory;
        _masterClients = new ConcurrentDictionary<string, ModbusTcpMasterClient>();
        _lastPolledValues = new ConcurrentDictionary<string, ushort[]>();

        _logger.LogInformation("DO to AO service initialized with {GroupCount} groups", _deviceMapping.DoMapAoGroups.Count);
    }

    /// <summary>
    /// 處理DO變更事件，查找對應的映射並寫入遠端Holding Register
    /// </summary>
    /// <param name="doAddress">DO位址</param>
    /// <param name="value">DO值</param>
    /// <returns>處理是否成功</returns>
    public async Task<bool> ProcessDoChangeAsync(ushort doAddress, bool value)
    {
        if (_disposed)
            return false;

        try
        {
            _logger.LogInformation("Received DO change request: DoAddress={DoAddress}, Value={Value}", doAddress, value);

            var processedCount = 0;
            var successCount = 0;

            // 遍歷所有群組，查找包含此DO位址的映射
            foreach (var group in _deviceMapping.DoMapAoGroups)
            {
                _logger.LogDebug("Checking group: {GroupName}, MappingCount={MappingCount}", group.GroupName, group.Mappings.Count);

                var mapping = group.Mappings.FirstOrDefault(m => m.LocalDoAddress == doAddress);
                if (mapping != null)
                {
                    processedCount++;
                    _logger.LogInformation("Found DO mapping: Group={GroupName}, DoAddress={DoAddress}, AoAddress={AoAddress}, Remote={RemoteIp}:{RemotePort}",
                        group.GroupName, doAddress, mapping.RemoteHoldingRegisterAddress, group.RemoteIp, group.RemotePort);

                    // 取得或建立ModbusTCP Master客戶端
                    var masterClient = GetOrCreateMasterClient(group);
                    if (masterClient != null)
                    {
                        // 決定要寫入的值
                        var writeValue = value ? mapping.ValueWhenTrue : mapping.ValueWhenFalse;

                        _logger.LogInformation("Preparing to write remote Holding Register: Address={AoAddress}, Value={WriteValue}",
                            mapping.RemoteHoldingRegisterAddress, writeValue);

                        // 寫入遠端Holding Register
                        var writeSuccess = await masterClient.WriteSingleHoldingRegisterAsync(
                            mapping.RemoteHoldingRegisterAddress, writeValue);

                        if (writeSuccess)
                        {
                            successCount++;
                            _logger.LogInformation("✅ Successfully wrote remote Holding Register: Group={GroupName}, DoAddress={DoAddress}, AoAddress={AoAddress}, Value={Value}",
                                group.GroupName, doAddress, mapping.RemoteHoldingRegisterAddress, writeValue);
                        }
                        else
                        {
                            _logger.LogWarning("❌ Failed to write remote Holding Register: Group={GroupName}, DoAddress={DoAddress}, AoAddress={AoAddress}, Value={Value}",
                                group.GroupName, doAddress, mapping.RemoteHoldingRegisterAddress, writeValue);

                            // 不再手動清理客戶端，讓智慧型重試機制自動處理
                            // ModbusTcpMasterClient 已經有內建的重試和重連機制
                        }
                    }
                    else
                    {
                        _logger.LogError("❌ Unable to get ModbusTCP Master client: Group={GroupName}", group.GroupName);
                    }
                }
                else
                {
                    _logger.LogDebug("No mapping found for DO address {DoAddress} in group {GroupName}", doAddress, group.GroupName);
                }
            }

            if (processedCount == 0)
            {
                _logger.LogInformation("No mapping configuration found for DO address {DoAddress}", doAddress);
            }
            else
            {
                _logger.LogInformation("DO to AO processing completed: DoAddress={DoAddress}, ProcessedCount={ProcessedCount}, SuccessCount={SuccessCount}",
                    doAddress, processedCount, successCount);
            }

            return processedCount == 0 || successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "處理DO轉AO時發生異常: DO位址={DoAddress}, 值={Value}", doAddress, value);
            return false;
        }
    }

    /// <summary>
    /// 輪詢所有群組的Holding Register並更新DO狀態
    /// </summary>
    /// <returns>輪詢是否成功</returns>
    public async Task<bool> PollAllGroupsAsync()
    {
        if (_disposed)
            return false;

        var successCount = 0;
        var totalGroups = _deviceMapping.DoMapAoGroups.Count;

        for (int groupIndex = 0; groupIndex < _deviceMapping.DoMapAoGroups.Count; groupIndex++)
        {
            var group = _deviceMapping.DoMapAoGroups[groupIndex];
            
            try
            {
                // 如果不是第一個群組，增加小延遲避免多個群組同時進行大量網路操作
                if (groupIndex > 0)
                {
                    await Task.Delay(20); // 群組間 20ms 延遲
                }

                var success = await PollGroupAsync(group);
                if (success)
                    successCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while polling group: Group={GroupName}", group.GroupName);
                
                // 群組輪詢異常後稍微延遲再繼續下一個群組
                if (groupIndex < _deviceMapping.DoMapAoGroups.Count - 1)
                {
                    await Task.Delay(100);
                }
            }
        }

        //_logger.LogDebug("Polling completed: Success={SuccessCount}/{TotalGroups}", successCount, totalGroups);
        return successCount > 0;
    }

    /// <summary>
    /// 輪詢單一群組的Holding Register
    /// 支援智慧分段讀取，避免單次讀取數量過大，同時優化讀取範圍
    /// </summary>
    /// <param name="group">DO轉AO群組配置</param>
    /// <returns>輪詢是否成功</returns>
    private async Task<bool> PollGroupAsync(DoMapAoGroup group)
    {
        if (_disposed || group.Mappings.Count == 0)
            return false;

        try
        {
            // 取得或建立ModbusTCP Master客戶端
            var masterClient = GetOrCreateMasterClient(group);
            if (masterClient == null)
                return false;

            // 取得所有需要讀取的位址並排序
            var addresses = group.Mappings.Select(m => m.RemoteHoldingRegisterAddress).OrderBy(a => a).ToArray();
            if (addresses.Length == 0)
                return false;

            var clientKey = $"{group.RemoteIp}:{group.RemotePort}:{group.RemoteSlaveId}";
            var allReadSuccess = true;
            var hasChanges = false;

            // 生成智慧讀取範圍
            var readRanges = GenerateOptimizedReadRanges(addresses, group.MaxPollingQuantity);

            // _logger.LogDebug("Group {GroupName}: Generated {RangeCount} optimized read ranges for {AddressCount} addresses", 
            //     group.GroupName, readRanges.Count, addresses.Length);

            // 依序讀取每個範圍，增加延遲以避免過度負荷遠端設備
            for (int rangeIndex = 0; rangeIndex < readRanges.Count; rangeIndex++)
            {
                var range = readRanges[rangeIndex];
                
                try
                {
                    // 如果不是第一個範圍，增加小延遲確保請求不會同時發送
                    if (rangeIndex > 0)
                    {
                        await Task.Delay(10); // 10ms 延遲，確保不同時發送
                    }

                    // 讀取指定範圍的 Holding Register
                    var values = await masterClient.ReadHoldingRegistersAsync(range.StartAddress, range.Quantity);
                    if (values == null)
                    {
                        _logger.LogWarning("Failed to read Holding Register range: Group={GroupName}, StartAddress={StartAddress}, Quantity={Quantity}, Range={RangeIndex}/{TotalRanges}",
                            group.GroupName, range.StartAddress, range.Quantity, rangeIndex + 1, readRanges.Count);
                        allReadSuccess = false;

                        // 不再手動清理客戶端，讓智慧型重試機制自動處理
                        // ModbusTcpMasterClient 已經有內建的重試和重連機制

                        continue;
                    }

                    // 處理讀取到的值
                    var rangeHasChanges = await ProcessReadValues(group, range, values, clientKey);
                    if (rangeHasChanges)
                        hasChanges = true;

                    // 只在有變更時才記錄成功讀取，減少日誌量
                    if (rangeHasChanges)
                    {
                        _logger.LogDebug("Successfully read range with changes: Group={GroupName}, StartAddress={StartAddress}, Quantity={Quantity}, Range={RangeIndex}/{TotalRanges}",
                            group.GroupName, range.StartAddress, range.Quantity, rangeIndex + 1, readRanges.Count);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Exception while reading range: Group={GroupName}, StartAddress={StartAddress}, Quantity={Quantity}, Range={RangeIndex}/{TotalRanges}",
                        group.GroupName, range.StartAddress, range.Quantity, rangeIndex + 1, readRanges.Count);
                    allReadSuccess = false;

                    // 不再手動清理客戶端，讓智慧型重試機制自動處理
                    // ModbusTcpMasterClient 已經有內建的異常處理和重連機制
                }
            }

            if (hasChanges)
            {
                _logger.LogInformation("Group polling completed with changes detected: Group={GroupName}, ReadRanges={RangeCount}, Success={AllSuccess}", 
                    group.GroupName, readRanges.Count, allReadSuccess);
            }

            return allReadSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while polling group: Group={GroupName}", group.GroupName);
            return false;
        }
    }

    /// <summary>
    /// 生成優化的讀取範圍列表
    /// 將連續的位址合併，但不超過最大讀取數量限制
    /// </summary>
    /// <param name="addresses">已排序的位址陣列</param>
    /// <param name="maxQuantity">單次讀取的最大數量</param>
    /// <returns>優化的讀取範圍列表</returns>
    private List<ReadRange> GenerateOptimizedReadRanges(ushort[] addresses, ushort maxQuantity)
    {
        var ranges = new List<ReadRange>();
        if (addresses.Length == 0)
            return ranges;

        var currentStart = addresses[0];
        var currentEnd = addresses[0];

        for (int i = 1; i < addresses.Length; i++)
        {
            var currentAddress = addresses[i];
            
            // 檢查是否可以擴展當前範圍
            var potentialQuantity = (ushort)(currentAddress - currentStart + 1);
            
            if (currentAddress == currentEnd + 1 && potentialQuantity <= maxQuantity)
            {
                // 連續位址且不超過最大數量限制，擴展當前範圍
                currentEnd = currentAddress;
            }
            else
            {
                // 不連續或超過最大數量限制，結束當前範圍並開始新範圍
                ranges.Add(new ReadRange
                {
                    StartAddress = currentStart,
                    Quantity = (ushort)(currentEnd - currentStart + 1)
                });
                
                currentStart = currentAddress;
                currentEnd = currentAddress;
            }
        }

        // 加入最後一個範圍
        ranges.Add(new ReadRange
        {
            StartAddress = currentStart,
            Quantity = (ushort)(currentEnd - currentStart + 1)
        });

        return ranges;
    }

    /// <summary>
    /// 處理讀取到的值，檢查變更並觸發事件
    /// </summary>
    /// <param name="group">群組配置</param>
    /// <param name="range">讀取範圍</param>
    /// <param name="values">讀取到的值</param>
    /// <param name="clientKey">客戶端識別鍵</param>
    /// <returns>是否有值變更</returns>
    private async Task<bool> ProcessReadValues(DoMapAoGroup group, ReadRange range, ushort[] values, string clientKey)
    {
        var hasChanges = false;
        
        // 取得或建立該範圍的上次值記錄
        var rangeKey = $"{clientKey}:{range.StartAddress}";
        var lastValues = _lastPolledValues.GetOrAdd(rangeKey, new ushort[range.Quantity]);

        // 檢查是否為初次讀取（用於強制同步）
        var isFirstRead = lastValues.All(v => v == 0) && _lastPolledValues.ContainsKey(rangeKey) == false;

        // 確保陣列大小正確
        if (lastValues.Length != range.Quantity)
        {
            lastValues = new ushort[range.Quantity];
            _lastPolledValues[rangeKey] = lastValues;
            isFirstRead = true; // 重新建立陣列時也視為初次讀取
        }

        for (int i = 0; i < Math.Min(values.Length, lastValues.Length); i++)
        {
            var registerAddress = (ushort)(range.StartAddress + i);
            var currentValue = values[i];
            var lastValue = lastValues[i];
            
            // 查找對應的DO映射
            var mapping = group.Mappings.FirstOrDefault(m => m.RemoteHoldingRegisterAddress == registerAddress);
            if (mapping != null)
            {
                var newDoState = currentValue != 0;
                
                // 值有變更或初次讀取時都要觸發同步
                if (currentValue != lastValue || isFirstRead)
                {
                    hasChanges = true;
                    
                    if (isFirstRead)
                    {
                        _logger.LogInformation("Initial sync from remote: Group={GroupName}, Address={Address}, Value={Value}, DoState={DoState}",
                            group.GroupName, registerAddress, currentValue, newDoState);
                    }
                    else
                    {
                        _logger.LogDebug("Detected Holding Register change: Group={GroupName}, Address={Address}, OldValue={OldValue}, NewValue={NewValue}, DoState={DoState}",
                            group.GroupName, registerAddress, lastValue, currentValue, newDoState);
                    }

                    // 觸發DO狀態變更事件
                    DoStateChanged?.Invoke(mapping.LocalDoAddress, newDoState);
                }
            }

            // 更新記錄的值
            lastValues[i] = currentValue;
        }

        return hasChanges;
    }

    /// <summary>
    /// 取得或建立ModbusTCP Master客戶端
    /// </summary>
    /// <param name="group">DO轉AO群組配置</param>
    /// <returns>ModbusTCP Master客戶端</returns>
    private ModbusTcpMasterClient? GetOrCreateMasterClient(DoMapAoGroup group)
    {
        if (_disposed)
            return null;

        var clientKey = $"{group.RemoteIp}:{group.RemotePort}:{group.RemoteSlaveId}";

        // 檢查現有客戶端是否仍然有效
        if (_masterClients.TryGetValue(clientKey, out var existingClient))
        {
            // 檢查連線狀態，如果已斷線則清理並重新創建
            if (!existingClient.IsConnected)
            {
                _logger.LogWarning("Detected disconnected client, removing and recreating: Group={GroupName}, Remote={RemoteIp}:{RemotePort}",
                    group.GroupName, group.RemoteIp, group.RemotePort);
                
                // 清理失效連線
                if (_masterClients.TryRemove(clientKey, out var removedClient))
                {
                    try
                    {
                        removedClient.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Exception while disposing failed client: {ClientKey}", clientKey);
                    }
                }
            }
            else
            {
                // 連線正常，直接返回
                return existingClient;
            }
        }

        // 創建新的客戶端
        try
        {
            _logger.LogInformation("Creating new ModbusTCP Master client: Group={GroupName}, Remote={RemoteIp}:{RemotePort}, SlaveId={SlaveId}",
                group.GroupName, group.RemoteIp, group.RemotePort, group.RemoteSlaveId);

            var clientLogger = _loggerFactory.CreateLogger<ModbusTcpMasterClient>();
            var newClient = new ModbusTcpMasterClient(group.RemoteIp, group.RemotePort, group.RemoteSlaveId, clientLogger);
            
            // 嘗試加入到字典中
            var addedClient = _masterClients.GetOrAdd(clientKey, newClient);
            
            // 如果返回的不是我們創建的客戶端，說明已經有其他線程創建了，釋放我們創建的
            if (addedClient != newClient)
            {
                newClient.Dispose();
                return addedClient;
            }
            
            return newClient;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create ModbusTCP Master client: Group={GroupName}, Remote={RemoteIp}:{RemotePort}",
                group.GroupName, group.RemoteIp, group.RemotePort);
            return null;
        }
    }

    /// <summary>
    /// 取得所有群組的連線狀態
    /// </summary>
    /// <returns>連線狀態資訊</returns>
    public Dictionary<string, bool> GetConnectionStatus()
    {
        var status = new Dictionary<string, bool>();

        foreach (var group in _deviceMapping.DoMapAoGroups)
        {
            var clientKey = $"{group.RemoteIp}:{group.RemotePort}:{group.RemoteSlaveId}";
            var groupKey = $"{group.GroupName} ({clientKey})";

            if (_masterClients.TryGetValue(clientKey, out var client))
            {
                status[groupKey] = client.IsConnected;
            }
            else
            {
                status[groupKey] = false;
            }
        }

        return status;
    }

    /// <summary>
    /// 手動重新連線所有客戶端
    /// </summary>
    /// <returns>重新連線的結果</returns>
    public async Task<Dictionary<string, bool>> ReconnectAllAsync()
    {
        var results = new Dictionary<string, bool>();

        foreach (var kvp in _masterClients)
        {
            try
            {
                await kvp.Value.DisconnectAsync();
                var connected = await kvp.Value.ConnectAsync();
                results[kvp.Key] = connected;

                _logger.LogInformation("Reconnection result: {ClientKey} = {Connected}", kvp.Key, connected);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred during reconnection: {ClientKey}", kvp.Key);
                results[kvp.Key] = false;
            }
        }

        return results;
    }

    /// <summary>
    /// 強制同步所有群組的遠端狀態到本地COIL
    /// 用於系統啟動時確保本地狀態與遠端一致
    /// </summary>
    /// <returns>同步是否成功</returns>
    public async Task<bool> ForceSyncAllGroupsAsync()
    {
        if (_disposed)
            return false;

        _logger.LogInformation("Starting force sync of all groups from remote to local COIL...");
        
        var successCount = 0;
        var totalGroups = _deviceMapping.DoMapAoGroups.Count;

        // 清除所有記錄的值，強制重新同步
        _lastPolledValues.Clear();

        for (int groupIndex = 0; groupIndex < _deviceMapping.DoMapAoGroups.Count; groupIndex++)
        {
            var group = _deviceMapping.DoMapAoGroups[groupIndex];
            
            try
            {
                _logger.LogInformation("Force syncing group: {GroupName}", group.GroupName);
                
                if (groupIndex > 0)
                {
                    await Task.Delay(50); // 群組間延遲
                }

                var success = await PollGroupAsync(group);
                if (success)
                {
                    successCount++;
                    _logger.LogInformation("Force sync completed for group: {GroupName}", group.GroupName);
                }
                else
                {
                    _logger.LogWarning("Force sync failed for group: {GroupName}", group.GroupName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred during force sync for group: Group={GroupName}", group.GroupName);
            }
        }

        var syncSuccess = successCount > 0;
        _logger.LogInformation("Force sync completed: Success={SuccessCount}/{TotalGroups}, Overall={SyncSuccess}", 
            successCount, totalGroups, syncSuccess ? "SUCCESS" : "FAILED");
        
        return syncSuccess;
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _logger.LogInformation("Disposing DO to AO service resources...");

            // 釋放所有ModbusTCP Master客戶端
            foreach (var kvp in _masterClients)
            {
                try
                {
                    kvp.Value.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Exception occurred while disposing ModbusTCP Master client: {ClientKey}", kvp.Key);
                }
            }

            _masterClients.Clear();
            _disposed = true;

            _logger.LogInformation("DO to AO service resources disposed");
        }
    }
}
