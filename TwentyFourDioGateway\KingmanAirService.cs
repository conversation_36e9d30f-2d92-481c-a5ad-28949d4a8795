using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace TwentyFourDioGateway;

/// <summary>
/// 空調控制服務，管理空調控制和狀態監控
/// 支援雙向功能：輪詢讀取空調狀態更新本地DI，以及本地DO寫入控制空調開關機
/// </summary>
public class KingmanAirService : IDisposable
{
    private readonly DeviceMapping _deviceMapping;
    private readonly ILogger<KingmanAirService> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly ConcurrentDictionary<string, KingmanAirClient> _airClients;
    private readonly ConcurrentDictionary<string, Dictionary<byte, bool>> _lastPolledStatus; // 記錄上次輪詢的狀態
    private bool _disposed = false;

    /// <summary>
    /// DI狀態變更事件，當輪詢發現空調狀態變更時觸發
    /// </summary>
    public event Action<ushort, bool>? DiStateChanged;

    /// <summary>
    /// DO狀態變更事件，當DI狀態變更時同步更新DO狀態
    /// </summary>
    public event Action<ushort, bool>? DoStateChanged;

    /// <summary>
    /// 建構函式
    /// </summary>
    /// <param name="deviceMappingOptions">設備映射配置</param>
    /// <param name="logger">日誌記錄器</param>
    /// <param name="loggerFactory">日誌工廠</param>
    public KingmanAirService(
        IOptions<DeviceMapping> deviceMappingOptions,
        ILogger<KingmanAirService> logger,
        ILoggerFactory loggerFactory)
    {
        _deviceMapping = deviceMappingOptions.Value;
        _logger = logger;
        _loggerFactory = loggerFactory;
        _airClients = new ConcurrentDictionary<string, KingmanAirClient>();
        _lastPolledStatus = new ConcurrentDictionary<string, Dictionary<byte, bool>>();

        _logger.LogInformation("Kingman Air service initialized with {GroupCount} groups", _deviceMapping.KingmanAirGroups.Count);
    }

    /// <summary>
    /// 處理DO變更事件，查找對應的映射並控制空調開關機
    /// </summary>
    /// <param name="doAddress">DO位址</param>
    /// <param name="value">DO值 (true=開機, false=關機)</param>
    /// <returns>處理是否成功</returns>
    public async Task<bool> ProcessDoChangeAsync(ushort doAddress, bool value)
    {
        if (_disposed)
            return false;

        try
        {
            _logger.LogInformation("Received air conditioner DO change request: DoAddress={DoAddress}, Value={Value}", doAddress, value);

            var processedCount = 0;
            var successCount = 0;

            // 遍歷所有群組，查找包含此DO位址的映射
            foreach (var group in _deviceMapping.KingmanAirGroups)
            {
                _logger.LogDebug("Checking air conditioner group: {GroupName}, MappingCount={MappingCount}", group.GroupName, group.Mappings.Count);

                var mapping = group.Mappings.FirstOrDefault(m => m.DoAddress == doAddress);
                if (mapping != null)
                {
                    processedCount++;
                    _logger.LogInformation("Found air conditioner DO mapping: Group={GroupName}, DoAddress={DoAddress}, AirConditionerId={AirConditionerId}, Remote={RemoteIp}:{RemotePort}",
                        group.GroupName, doAddress, mapping.AirConditionerId, group.RemoteIp, group.RemotePort);

                    // 取得或建立空調客戶端
                    var airClient = GetOrCreateAirClient(group);
                    if (airClient != null)
                    {
                        bool commandSuccess;
                        if (value)
                        {
                            // 開機指令
                            _logger.LogInformation("Sending START command to air conditioner: ID={AirConditionerId}", mapping.AirConditionerId);
                            commandSuccess = await airClient.StartAirConditionerAsync(mapping.AirConditionerId);
                        }
                        else
                        {
                            // 關機指令
                            _logger.LogInformation("Sending STOP command to air conditioner: ID={AirConditionerId}", mapping.AirConditionerId);
                            commandSuccess = await airClient.StopAirConditionerAsync(mapping.AirConditionerId);
                        }

                        if (commandSuccess)
                        {
                            successCount++;
                            _logger.LogInformation("✅ Successfully sent command to air conditioner: Group={GroupName}, DoAddress={DoAddress}, AirConditionerId={AirConditionerId}, Command={Command}",
                                group.GroupName, doAddress, mapping.AirConditionerId, value ? "START" : "STOP");
                        }
                        else
                        {
                            _logger.LogWarning("❌ Failed to send command to air conditioner: Group={GroupName}, DoAddress={DoAddress}, AirConditionerId={AirConditionerId}, Command={Command}",
                                group.GroupName, doAddress, mapping.AirConditionerId, value ? "START" : "STOP");
                        }
                    }
                    else
                    {
                        _logger.LogError("❌ Unable to get air conditioner client: Group={GroupName}", group.GroupName);
                    }
                }
                else
                {
                    _logger.LogDebug("No air conditioner mapping found for DO address {DoAddress} in group {GroupName}", doAddress, group.GroupName);
                }
            }

            if (processedCount == 0)
            {
                _logger.LogInformation("No air conditioner mapping configuration found for DO address {DoAddress}", doAddress);
            }
            else
            {
                _logger.LogInformation("Air conditioner DO processing completed: DoAddress={DoAddress}, ProcessedCount={ProcessedCount}, SuccessCount={SuccessCount}",
                    doAddress, processedCount, successCount);
            }

            return processedCount == 0 || successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "處理空調DO控制時發生異常: DO位址={DoAddress}, 值={Value}", doAddress, value);
            return false;
        }
    }

    /// <summary>
    /// 輪詢所有群組的空調狀態並更新DI狀態（並行處理）
    /// </summary>
    /// <returns>輪詢是否成功</returns>
    public async Task<bool> PollAllGroupsAsync()
    {
        if (_disposed)
            return false;

        var totalGroups = _deviceMapping.KingmanAirGroups.Count;
        if (totalGroups == 0)
            return false;

        // 並行輪詢所有群組
        var tasks = _deviceMapping.KingmanAirGroups.Select(group =>
            Task.Run(async () =>
            {
                try
                {
                    return await PollGroupAsync(group);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Exception occurred while polling air conditioner group: Group={GroupName}", group.GroupName);
                    return false;
                }
            })
        ).ToArray();

        var results = await Task.WhenAll(tasks);
        var successCount = results.Count(r => r);

        //_logger.LogDebug("Air conditioner polling completed: Success={SuccessCount}/{TotalGroups}", successCount, totalGroups);
        return successCount > 0;
    }

    /// <summary>
    /// 輪詢單一群組的空調狀態
    /// </summary>
    /// <param name="group">空調群組配置</param>
    /// <returns>輪詢是否成功</returns>
    private async Task<bool> PollGroupAsync(KingmanAirGroup group)
    {
        if (_disposed || group.Mappings.Count == 0)
            return false;

        try
        {
            // 取得或建立空調客戶端
            var airClient = GetOrCreateAirClient(group);
            if (airClient == null)
                return false;

            // 取得上次輪詢的狀態
            var clientKey = $"{group.RemoteIp}:{group.RemotePort}";
            var lastStatus = _lastPolledStatus.GetOrAdd(clientKey, new Dictionary<byte, bool>());

            var hasChanges = false;
            var successCount = 0;

            // 依序輪詢每台空調的狀態 - 簡單直接，遇到問題直接跳過
            foreach (var mapping in group.Mappings)
            {
                try
                {
                    // 查詢空調狀態，如果沒有回應或回應有問題（包括校驗碼錯誤），直接跳過
                    var currentStatus = await airClient.QueryAirConditionerStatusAsync(mapping.AirConditionerId);

                    if (currentStatus.HasValue)
                    {
                        successCount++;

                        // 檢查狀態是否有變更
                        if (!lastStatus.ContainsKey(mapping.AirConditionerId) || lastStatus[mapping.AirConditionerId] != currentStatus.Value)
                        {
                            hasChanges = true;
                            var oldStatus = lastStatus.ContainsKey(mapping.AirConditionerId) ? lastStatus[mapping.AirConditionerId] : (bool?)null;

                            _logger.LogInformation("Air conditioner status changed: Group={GroupName}, AirConditionerId={AirConditionerId}, OldStatus={OldStatus}, NewStatus={NewStatus}",
                                group.GroupName, mapping.AirConditionerId, oldStatus?.ToString() ?? "Unknown", currentStatus.Value ? "ON" : "OFF");

                            // 更新記錄的狀態
                            lastStatus[mapping.AirConditionerId] = currentStatus.Value;

                            // 觸發DI狀態變更事件
                            DiStateChanged?.Invoke(mapping.DiAddress, currentStatus.Value);

                            // 同時更新對應的DO狀態 (DO狀態由DI狀態決定)
                            DoStateChanged?.Invoke(mapping.DoAddress, currentStatus.Value);
                        }
                    }
                    else
                    {
                        // 查詢失敗（超時、無回應、校驗碼錯誤等），直接跳過，等下一輪再試
                        _logger.LogDebug("Skipping air conditioner due to query failure: Group={GroupName}, AirConditionerId={AirConditionerId}",
                            group.GroupName, mapping.AirConditionerId);
                    }
                }
                catch (Exception ex)
                {
                    // 發生異常，記錄日誌後直接跳過，等下一輪再試
                    _logger.LogWarning(ex, "Exception while querying air conditioner, skipping: Group={GroupName}, AirConditionerId={AirConditionerId}",
                        group.GroupName, mapping.AirConditionerId);
                }
            }

            if (hasChanges)
            {
                _logger.LogInformation("Air conditioner status changes detected in group: {GroupName}, SuccessCount={SuccessCount}/{TotalCount}",
                    group.GroupName, successCount, group.Mappings.Count);
            }

            return successCount > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while polling air conditioner group: Group={GroupName}", group.GroupName);
            return false;
        }
    }

    /// <summary>
    /// 取得或建立空調客戶端
    /// </summary>
    /// <param name="group">空調群組配置</param>
    /// <returns>空調客戶端</returns>
    private KingmanAirClient? GetOrCreateAirClient(KingmanAirGroup group)
    {
        if (_disposed)
            return null;

        var clientKey = $"{group.RemoteIp}:{group.RemotePort}";

        return _airClients.GetOrAdd(clientKey, key =>
        {
            _logger.LogInformation("Creating new air conditioner client: Group={GroupName}, Remote={RemoteIp}:{RemotePort}",
                group.GroupName, group.RemoteIp, group.RemotePort);

            var clientLogger = _loggerFactory.CreateLogger<KingmanAirClient>();
            return new KingmanAirClient(group.RemoteIp, group.RemotePort, clientLogger, group.GroupName);
        });
    }

    /// <summary>
    /// 檢查指定的DO位址是否為空調控制位址
    /// </summary>
    /// <param name="doAddress">DO位址</param>
    /// <returns>如果是空調控制位址則返回true</returns>
    public bool IsAirConditionerDoAddress(ushort doAddress)
    {
        return _deviceMapping.KingmanAirGroups
            .SelectMany(group => group.Mappings)
            .Any(mapping => mapping.DoAddress == doAddress);
    }

    /// <summary>
    /// 取得所有群組的連線狀態
    /// </summary>
    /// <returns>連線狀態資訊</returns>
    public Dictionary<string, bool> GetConnectionStatus()
    {
        var status = new Dictionary<string, bool>();

        foreach (var group in _deviceMapping.KingmanAirGroups)
        {
            var clientKey = $"{group.RemoteIp}:{group.RemotePort}";
            var groupKey = $"{group.GroupName} ({clientKey})";

            if (_airClients.TryGetValue(clientKey, out var client))
            {
                status[groupKey] = client.IsConnected;
            }
            else
            {
                status[groupKey] = false;
            }
        }

        return status;
    }

    /// <summary>
    /// 釋放資源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var client in _airClients.Values)
            {
                client.Dispose();
            }
            _airClients.Clear();
            _disposed = true;
        }
    }
}
