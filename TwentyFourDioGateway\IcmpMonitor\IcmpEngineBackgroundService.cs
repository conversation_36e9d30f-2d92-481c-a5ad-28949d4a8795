﻿
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using TwentyFourDioGateway.Data;

namespace TwentyFourDioGateway.IcmpMonitor
{
    public class PingNode
    {
        public ushort ModbusAddress { get; set; }
        public string Ip { get; set; }
        public bool Status { get; set; }
        public DateTime LastAliveTime { get; set; }
        public UInt64 DisconnectCount { get; set; } = 0;
    }
    public class IcmpEngineBackgroundService : BackgroundService
    {
     

        private List<PingNode> pingNodes = new List<PingNode>();
        private readonly Setting _options;
        private readonly DeviceMapping _deviceMapping;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<IcmpEngineBackgroundService> _logger;
        public IcmpEngineBackgroundService(
            IOptions<Setting> options,
            IOptions<DeviceMapping> deviceMapping,
            IServiceProvider serviceProvider, 
            ILogger<IcmpEngineBackgroundService> logger)
        {
            _options = options!.Value;
            _deviceMapping = deviceMapping!.Value;
            _serviceProvider = serviceProvider;
            _logger = logger;
            //pingNodes = Enumerable.Range(1, 254).Select(i => new PingNode() { Id = i, Ip = $"192.168.1.{i}" }).ToList();
            //pingNodes = Enumerable.Range(0, 254)
            //    .SelectMany(i => Enumerable.Range(1, 254)
            //        .Select(j => new PingNode() { ModbusAddress = (ushort)((i - 1) * 254 + j), Ip = $"192.168.{i}.{j}" }))
            //    .ToList();
            
        }
        private List<PingNode> BuildPingNodes()
        {
            var db = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>();
            return _deviceMapping.PingIps.SelectMany(ip => Enumerable.Range(0, ip.Length).Select( i =>
            {
                var lastDigit = byte.Parse(ip.StartAddress.Substring(ip.StartAddress.LastIndexOf(".")+1));
                var withoutLastDigit = ip.StartAddress.Substring(0, ip.StartAddress.LastIndexOf("."));
                var status =  db.Nodes.Where(n => n.AddressType == Models.ModbusAddressType.DiscreteInput)
                        .Where(n => n.Address == (ip.ModbusAddress + i)).FirstOrDefault();
                var newStatus = false;
                if (status is null) {

                }
                else
                {
                    newStatus = status.Value;
                }
                return new PingNode()
                {
                    Ip = $"{withoutLastDigit}.{lastDigit + i}",
                    ModbusAddress = (ushort)(ip.ModbusAddress + i),
                    Status = status is null ? false : status.Value,
                    LastAliveTime = DateTime.Now
                };
            })).ToList();
        }
        private async Task PingAsync(PingNode pingNode)
        {
            try
            {

                using (Ping ping = new Ping())
                {
                    PingReply reply = await ping.SendPingAsync(pingNode.Ip, _deviceMapping.PingTimeout);
                    if (reply.Status == IPStatus.Success)
                    {
                        _logger.LogDebug($"Ping to {pingNode.Ip} successful!");
                    }
                    else
                    {
                        _logger.LogDebug($"Ping to {pingNode.Ip} failed! {reply.Status}");
                    }
                }
            }
            catch (Exception e)
            {
                _logger.LogWarning($"Ping failed: {e.Message}");
            }

        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            PingPool pingPool = new PingPool((int)(_deviceMapping.PingBatchSize * 1.05));
            //int _deviceMapping.PingRetryAttempts = 3; // 最大重試次數
            TimeSpan retryDelay = TimeSpan.FromMilliseconds(100); // 每次重試的延遲
            pingNodes = BuildPingNodes();
            _logger.LogInformation($"Start running ICMP monitor with {pingNodes.Count} nodes.");
            while (!stoppingToken.IsCancellationRequested)
            {
                Stopwatch sw = new Stopwatch();
                try
                {
                    var gatewayService = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<GatewayService>();
                    sw = Stopwatch.StartNew();

                    var semaphore = new SemaphoreSlim(_deviceMapping.PingBatchSize);
                    var pingTasks = pingNodes.Select(async pingNode =>
                    {
                        if (stoppingToken.IsCancellationRequested)
                        {
                            return;
                        }
                        await semaphore.WaitAsync(stoppingToken);

                        Ping ping = null!;
                        try
                        {
                            ping = pingPool.GetPing();
                            for (int attempt = 0; attempt < _deviceMapping.PingRetryAttempts; attempt++)
                            {
                                try
                                {
                                    PingReply reply = await ping.SendPingAsync(pingNode.Ip, _deviceMapping.PingTimeout);
                                   
                                    if (reply.Status == IPStatus.Success)
                                    {
                                        pingNode.LastAliveTime = DateTime.Now;
                                        if (pingNode.Status)
                                        {
                                            _logger.LogInformation($"Found new node {pingNode.Ip}.");
                                            pingNode.Status = false;                                            
                                            await gatewayService.UpdateStatusAsync(Models.ModbusAddressType.DiscreteInput, pingNode.ModbusAddress, pingNode.Status);
                                        }
                                        break; // 成功後退出重試循環
                                    }
                                    else
                                    {
                                        if (attempt < _deviceMapping.PingRetryAttempts - 1)
                                        {
                                            //_logger.LogWarning($"Ping to {pingNode.Ip} failed (Attempt {attempt + 1}/{_deviceMapping.PingRetryAttempts}). Retrying...");
                                            await Task.Delay(retryDelay, stoppingToken);
                                        }
                                        else
                                        {
                                            if (!pingNode.Status)
                                            {
                                                if ((DateTime.Now - pingNode.LastAliveTime) > TimeSpan.FromMinutes(_deviceMapping.PingDeadMinutes))
                                                {
                                                    _logger.LogInformation($"Node {pingNode.Ip} is missing after {_deviceMapping.PingDeadMinutes} minutes. Disconnect count: {++pingNode.DisconnectCount}");
                                                    pingNode.Status = true;
                                                    await gatewayService.UpdateStatusAsync(Models.ModbusAddressType.DiscreteInput, pingNode.ModbusAddress, pingNode.Status);
                                                }
                                                else
                                                {
                                                    //_logger.LogInformation($"Node {pingNode.Ip} is missing after {_deviceMapping.PingRetryAttempts} retry attempt. But no status changed");
                                                }
                                            }
                                        
                                        }
                                    }
                                }
                                catch (PingException ex)
                                {
                                    _logger.LogWarning($"Ping exception on {pingNode.Ip}: {ex.Message}");
                                    if (attempt < _deviceMapping.PingRetryAttempts - 1)
                                    {
                                        await Task.Delay(retryDelay, stoppingToken);
                                    }
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            _logger.LogWarning($"Ping operation failed for {pingNode.Ip}: {e.Message}");
                        }
                        finally
                        {
                            pingPool.ReturnPing(ping); // 將 Ping 實例歸還到池中。
                            semaphore.Release();
                        }
                    });

                    await Task.WhenAll(pingTasks);
                }
                catch (Exception e)
                {
                    _logger.LogError($"An error occurred in the main loop: {e.Message}");
                }
                finally
                {
                    sw.Stop();
                    //_logger.LogInformation($"Ping finished: {sw.Elapsed}");
                    //_logger.LogInformation($"Successes: {pingNodes.Where(p => p.Status).Count()}");

                    var delayTime = 5;// _deviceMapping.PingBatchDelayInSeconds - sw.Elapsed.TotalSeconds;
                    //if (delayTime < 5)
                    //{
                    //    delayTime = 5;
                    //}
                    //_logger.LogInformation($"Delay: {delayTime}");
                    await Task.Delay(TimeSpan.FromSeconds(delayTime), stoppingToken);
                }
            }
        }

      
    }
}
