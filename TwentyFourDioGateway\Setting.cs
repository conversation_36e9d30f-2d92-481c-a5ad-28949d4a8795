﻿namespace TwentyFourDioGateway
{
    public class Setting
    {
        public string ModbusTCPSlaveBindIpAddress { get; set; } = "0.0.0.0";
        public ushort ModbusTCPSlaveBindPort { get; set; } = 502;
        public bool EnablePingRongDawuNursing { get; set; } = false;
        public bool EnableVideoRecording { get; set; } = false;
        public bool EnableSafetyMessage { get; set; } = false;
        public bool EnableHundureIllegalCardNotification { get; set; } = false;
        public bool EnableCwbWeather { get; set; } = false;

        /// <summary>
        /// 是否啟用 DO轉AO 功能
        /// </summary>
        public bool EnableDoToAo { get; set; } = false;

        /// <summary>
        /// 是否啟用空調控制功能
        /// </summary>
        public bool EnableKingmanAir { get; set; } = false;

        /// <summary>
        /// 是否啟用 Asterisk PBX 分機狀態監控功能
        /// </summary>
        public bool EnableAsteriskMonitoring { get; set; } = false;

        /// <summary>
        /// Asterisk REST Interface (ARI) 伺服器 URL
        /// 格式: http://asterisk_server_ip:8088/ari
        /// </summary>
        public string AsteriskApiUrl { get; set; } = string.Empty;

        /// <summary>
        /// Asterisk REST Interface (ARI) 使用者名稱
        /// </summary>
        public string AsteriskApiUsername { get; set; } = string.Empty;

        /// <summary>
        /// Asterisk REST Interface (ARI) 密碼
        /// </summary>
        public string AsteriskApiPassword { get; set; } = string.Empty;

        /// <summary>
        /// Asterisk 狀態更新間隔 (毫秒)
        /// </summary>
        public int AsteriskUpdateIntervalMilliseconds { get; set; } = 5000;

        /// <summary>
        /// 是否啟用24DIO數位輸入狀態變化電子郵件通知
        /// </summary>
        public bool EnableDiEmailNotification { get; set; } = false;

        /// <summary>
        /// 24DIO數位輸入狀態變化電子郵件通知收件人列表
        /// </summary>
        public List<string> DiEmailRecipients { get; set; } = new List<string>();

        /// <summary>
        /// 24DIO數位輸入狀態變化電子郵件主旨
        /// </summary>
        public string DiEmailSubject { get; set; } = string.Empty;

        /// <summary>
        /// 24DIO數位輸入狀態變化電子郵件內容模板
        /// 支援以下變數:
        /// {DeviceId} - 裝置ID
        /// {DeviceIp} - 裝置IP地址
        /// {DiNumber} - DI編號 (1-24)
        /// {Status} - 狀態變化 (true/false)
        /// {DateTime} - 時間戳記 (格式: yyyy-MM-dd HH:mm:ss)
        /// </summary>
        public string DiEmailBodyTemplate { get; set; } = string.Empty;
    }
}
