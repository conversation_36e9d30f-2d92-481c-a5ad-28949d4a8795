﻿using FFMpegCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Channels;
using System.Threading.Tasks;
using TwentyFourDioGateway.Data;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway
{

    public class VideoRecordingHostedService : IHostedService
    {
        public readonly Channel<RecordingRequest> channel;
        //public readonly ConcurrentQueue<HiSharpRecordingRequest> queue = new ConcurrentQueue<HiSharpRecordingRequest>();
        private List<Task> _tasks = new List<Task>();
        private readonly CancellationTokenSource _cts = new CancellationTokenSource();
        //private ConcurrentDictionary<string,Task<bool>> _recordingTasks = new ConcurrentDictionary<string, Task<bool>>();
        private readonly Setting _setting;
        private readonly DeviceMapping _deviceMapping;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<VideoRecordingHostedService> _logger;
        private readonly GatewayService _gatewayService;
        public VideoRecordingHostedService(
            GatewayService gatewayService,
            ILogger<VideoRecordingHostedService> logger,
            IServiceProvider serviceProvider,
            VideoRecordingService videoRecordingService,
            IOptions<Setting> options,
            IOptions<DeviceMapping> deviceMapping)
        {
            _gatewayService = gatewayService;
            _logger = logger;
            _serviceProvider = serviceProvider;
            _setting = options.Value;
            _deviceMapping = deviceMapping.Value;
            foreach (var cam in _deviceMapping.VideoCameras)
            {
                if (string.IsNullOrWhiteSpace(cam.Username) && string.IsNullOrWhiteSpace(cam.Password))
                {
                    if (cam.Brand.ToLower() == "HISHARP".ToLower())
                    {
                        cam.Password = _deviceMapping.HiSharpDefaultPassword;
                        cam.Username = _deviceMapping.HiSharpDefaultUsername;
                    }
                    else if (cam.Brand.ToLower() == "FUHO".ToLower())
                    {
                        cam.Password = _deviceMapping.FuhoDefaultPassword;
                        cam.Username = _deviceMapping.FuhoDefaultUsername;
                    }
                }
            }
            //var hiSharpVideoRecordingService = serviceProvider.GetRequiredService<HiSharpVideoRecordingService>();
            channel = videoRecordingService.channel;
        }
        public Task StartAsync(CancellationToken cancellationToken)
        {
            //channel = hiSharpVideoRecordingService.channel;
            if (_setting.EnableVideoRecording)
            {                
                _logger.LogInformation("EnableHiSharpVideoRecording Enabled");
                _tasks.Add(Task.Run(() => Pool(_cts.Token)));
                _tasks.Add(Task.Run(() => CheckPool(_cts.Token)));
            }            

            return Task.CompletedTask;
        }

        public async Task Pool(CancellationToken token)
        {

            while (await channel.Reader.WaitToReadAsync(token))
            {
                var request = await channel.Reader.ReadAsync(token);
                //queue.Enqueue(request);
                var cam = _deviceMapping.VideoCameras
                    .Where(x => x.Id == request.VideoCamId)
                    .FirstOrDefault();
                if (cam != null)
                {
                    using (var db = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>())
                    {
                        db.RecordingRequests.Add(request);
                        await db.SaveChangesAsync();
                        _gatewayService.dataStore.CoilDiscretes[request.VideoCamId] = false;
                        await _gatewayService.UpdateStatusAsync(ModbusAddressType.Coil, (ushort)request.VideoCamId, false);
            
                        _logger.LogInformation($"{request.Time.ToString("yyyy/MM/dd HH:mm:ss")} cam id:{request.VideoCamId} added to queue.");
                    }
                    
                }        
                
                
                
            }
        }

        public async Task CheckPool(CancellationToken token)
        {
            _logger.LogInformation($"CheckPool start.");
            while (true)
            {
                var dequeued = false;
                try
                {
                    using (var db = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<AppDbContext>())
                    {
                        var targetSecondsAgo = DateTime.Now.Subtract(TimeSpan.FromSeconds(_deviceMapping.RecordWaitSeconds));
                        var requests = await db.RecordingRequests
                            .Where(r => !r.IsCompleted)
                            //.Where(r => DateTime.Now > r.Time.Add(TimeSpan.FromSeconds(20)))
                            .Where(r => r.Time < targetSecondsAgo)
                            .OrderBy(r => r.Time)
                            .Take(10)
                            .ToListAsync();
                        //_logger.LogInformation($"{requests.Count} requests try dequeue.");
                        if (requests.Any())
                        {
                            dequeued = true;
                            _logger.LogInformation($"{requests.Count} requests dequeued.");
                            foreach (var req in requests)
                            {
                                var cam = _deviceMapping.VideoCameras
                                    .Where(x => x.Id == req.VideoCamId)
                                    .FirstOrDefault();
                                if (cam != null)
                                {
                                    if (cam.Brand.ToLower() == "HISHARP".ToLower())
                                    {
                                        Stopwatch sw = Stopwatch.StartNew();
                                        _logger.LogInformation($"Start recording HiSharp {req.Time.ToString("yyyy/MM/dd HH:mm:ss")}_{req.VideoCamId}_{cam.Ip}.");
                                        //CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
                                        var recordStartingTime = req.Time.Subtract(TimeSpan.FromSeconds(7));
                                        var fileName = @$"{_deviceMapping.RecordPath}/{recordStartingTime.ToString("yyyyMMdd_HHmmss")}_{cam.Ip.Replace(".", "_")}.mp4";
                                        var url = @$"{_deviceMapping.PublicVideoUrlPrefix}/{recordStartingTime.ToString("yyyyMMdd_HHmmss")}_{cam.Ip.Replace(".", "_")}.mp4";
                                        var result = await HiSharpRecordVideo(req, cam, recordStartingTime, fileName, url);
                                        sw.Stop();
                                        _logger.LogInformation($"End recording HiSharp {req.Time.ToString("yyyy/MM/dd HH:mm:ss")}_{req.VideoCamId}_{cam.Ip}, IsSuccessed:{result}, duration: {sw.Elapsed.TotalSeconds} seconds.");
                                        req.IsCompleted = true;
                                        req.IsSuccessed = result;
                                        req.PublicVideoUrl = $"{url}";

                                        req.RecordDurationSeconds = (int)sw.Elapsed.TotalSeconds;

                                        await db.SaveChangesAsync();
                                    }
                                    else if (cam.Brand.ToLower() == "FUHO".ToLower())
                                    {
                                        Stopwatch sw = Stopwatch.StartNew();
                                        _logger.LogInformation($"Start recording Fuho {req.Time.ToString("yyyy/MM/dd HH:mm:ss")}_{req.VideoCamId}_{cam.Ip}.");
                                        //CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
                                        var recordStartingTime = req.Time.Subtract(TimeSpan.FromSeconds(7));
                                        var fileName = @$"{_deviceMapping.RecordPath}/{recordStartingTime.ToString("yyyyMMdd_HHmmss")}_{cam.Ip.Replace(".", "_")}.mp4";
                                        var url = @$"{_deviceMapping.PublicVideoUrlPrefix}/{recordStartingTime.ToString("yyyyMMdd_HHmmss")}_{cam.Ip.Replace(".", "_")}.mp4";
                                        var result = await FuhoRecordVideo(req, cam, recordStartingTime, fileName, url);
                                        sw.Stop();
                                        _logger.LogInformation($"End recording Fuho {req.Time.ToString("yyyy/MM/dd HH:mm:ss")}_{req.VideoCamId}_{cam.Ip}, IsSuccessed:{result}, duration: {sw.Elapsed.TotalSeconds} seconds.");
                                        req.IsCompleted = true;
                                        req.IsSuccessed = result;
                                        req.PublicVideoUrl = $"{url}";

                                        req.RecordDurationSeconds = (int)sw.Elapsed.TotalSeconds;

                                        await db.SaveChangesAsync();
                                    }
                                
                                }
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError($"CheckPool exception : {e.Message}");
                }
          
                //_logger.LogInformation($"CheckPool.");
                //if (!dequeued)
                //{
                await Task.Delay(10000, token);
                //}
            }
        }
        private async Task KillExisted()
        {
            Process[] processes = Process.GetProcessesByName("weemarecording");

            foreach (Process process in processes)
            {
                process.Kill();
                await process.WaitForExitAsync();
                _logger.LogWarning($"Killed process with PID: {process.Id}");
            }
        }
        public async Task<bool> RunHiSharpRecordingAsync(string url, string filename, CancellationToken token)
        {
            var exeName = "weemarecording.exe";
            var arguments = $" -nostdin -t 15 -rtsp_transport tcp -i \"{url}\" -acodec copy -vcodec copy {filename}.mp4"; //舊的錄影指令

            //新的錄影指令，為了適配IOS能正常播放而做轉檔
            arguments = $" -nostdin -t 15 -rtsp_transport tcp -i \"{url}\" -c:v libx264 -preset fast -crf 23 -c:a aac -b:a 128k -movflags +faststart {filename}";
            if (_deviceMapping.HiSharpUseVlcRecordingMode)
            {
                exeName = "vlc/weemarecording.exe";
                arguments = $" -vvv \"{url}\" --sout=\"#duplicate{{dst= std{{access=file,mux=mp4,dst='{filename}.mp4'}}}}\" --run-time=15 --play-and-exit -I dummy";

            }
            _logger.LogInformation($"{exeName} {arguments}");
            ProcessStartInfo startInfo = new ProcessStartInfo();
            startInfo.FileName = exeName;  // Executable to run
            startInfo.Arguments = arguments;  // Command line arguments
            startInfo.UseShellExecute = false; // Do not use shell, important for redirection
            startInfo.RedirectStandardOutput = true; // Capture output
            //startInfo.RedirectStandardError = true; // Capture error
            Process process = new Process();
            //process.OutputDataReceived += (sender, args) => _logger.LogDebug(args.Data);
            //process.ErrorDataReceived += (sender, args) => _logger.LogWarning(args.Data);
            process.StartInfo = startInfo;
            process.Start(); // Start process
            await process.WaitForExitAsync(token);
            return process.ExitCode == 0;
            //@" .\ffmpeg.exe -nostdin -i ""rtsp://admin:123456@192.168.3.86/chID=1&date=2024-02-05&time=15:00:00&timelen=10&action=playback&linkType=tcp"" -acodec copy -vcodec copy -t 12 -rtsp_transport tcp  ./abc1.avi"
            //return true;
        }
        public async Task<bool> FuhoRecordVideo(RecordingRequest req, VideoCamera camera, DateTime recordStartingTime, string fileName, string url)
        {
            var returnValue = false;
            try
            {
                //var recordStartingTime = req.Time.Subtract(TimeSpan.FromSeconds(7));
                recordStartingTime = recordStartingTime.AddSeconds(-1);
                var endTime = recordStartingTime.AddSeconds(11);
                var portString = camera.Port.HasValue ? ":" + camera.Port.Value : "";
                var playbackMode = _deviceMapping.HiSharpUsePlaybackMode ? "playback" : "backup";
                var startTimeUtcString = recordStartingTime.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
                var endTimeUtcString = endTime.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
                //_logger.LogInformation($"Recording {recordStartingTime.ToString("yyyy/MM/dd HH:mm:ss")}");
                var urlString = $"http://{camera.Ip}/recordlist.cgi?starttime={startTimeUtcString}&endtime={endTimeUtcString}&maxcount=999";
                req.RecordUrl = urlString;
                _logger.LogInformation(urlString);
                _logger.LogInformation(fileName);

                CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(_deviceMapping.RecordTimeoutSeconds));
                if (File.Exists(fileName))
                {
                    File.Delete(fileName);
                    _logger.LogInformation("File exists, deleted successfully.");
                }
                using (var client = _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<IHttpClientFactory>().CreateClient())
                {
                    var byteArray = Encoding.ASCII.GetBytes($"{camera.Username}:{camera.Password}");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
                    //GetAsync with "getIdUrl" with http basic auth


                    var response = await client.GetAsync(urlString);
                    response.EnsureSuccessStatusCode();
                    var content = await response.Content.ReadFromJsonAsync<FuhoRecordList>();
                    var firstRecordId = content.Recordlist.First().Id;
                    var recordVideoUrl = $"http://{camera.Ip}/playback.mp4?id={firstRecordId}&starttime={startTimeUtcString}&endtime={endTimeUtcString}";
                    //var path = @$"C:\test\{recordStartingTime.AddSeconds(1).ToString("yyyy_MM_dd_HH_mm_ss")}_to_{endTime.AddSeconds(-1).ToString("yyyy_MM_dd_HH_mm_ss")}.mp4";                    
                    var result = await client.GetByteArrayAsync(recordVideoUrl);
                     await File.WriteAllBytesAsync(fileName, result);
                }
                returnValue = true;
            }
            catch (Exception e)
            {
                //await KillExisted();
                _logger.LogError($"Recording exception: {e.Message}");
            }

            return returnValue;

        }
        public async Task<bool> HiSharpRecordVideo(RecordingRequest req, VideoCamera camera, DateTime recordStartingTime,string fileName,string url)
        {
            var returnValue = false;
            try
            {
                //var recordStartingTime = req.Time.Subtract(TimeSpan.FromSeconds(7));
                var portString = camera.Port.HasValue ? ":" + camera.Port.Value : "";
                var playbackMode = _deviceMapping.HiSharpUsePlaybackMode ? "playback" : "backup";
                //_logger.LogInformation($"Recording {recordStartingTime.ToString("yyyy/MM/dd HH:mm:ss")}");
                var urlString = @$"rtsp://{camera.Username}:{camera.Password}@{camera.Ip}{portString}/chID=0&date={recordStartingTime.ToString("yyyy-MM-dd")}&time={recordStartingTime.ToString("HH:mm:ss")}&timelen=30&action={playbackMode}";
                req.RecordUrl = urlString;
                _logger.LogInformation(urlString);
                var outputString = fileName;
                _logger.LogInformation(outputString);
                await KillExisted();
                CancellationTokenSource cts = new CancellationTokenSource(TimeSpan.FromSeconds(_deviceMapping.RecordTimeoutSeconds)); 
                if (File.Exists(outputString))
                {
                    File.Delete(outputString);
                    _logger.LogInformation("File exists, deleted successfully.");
                }
                returnValue = await RunHiSharpRecordingAsync(urlString, outputString, cts.Token);
                //Task<bool> delay = Task.Delay(TimeSpan.FromSeconds(_deviceMapping.HiSharpRecordTimeoutSeconds)).ContinueWith<bool>((t) => false);
                //var result = await Task.WhenAny<bool>(task, delay);
                //if (result == task)
                //{
                //    returnValue = await task;
                //}
                //else
                //{
                //    await KillFFmpegs();
                //    //Process[] ffmpegProcesses = Process.GetProcessesByName("ffmpeg.exe");

                //    //foreach (Process process in ffmpegProcesses)
                //    //{
                //    //    process.Kill();
                //    //    _logger.LogWarning($"Killed FFmpeg process with PID: {process.Id}");
                //    //}
                //    _logger.LogWarning($"Recording {recordStartingTime.ToString("yyyy/MM/dd HH:mm:ss")}_{camera.Ip} timeout.");
                //}
                //using (var task = FFMpegArguments
                //  .FromUrlInput(new Uri(urlString))
                //  .OutputToFile(outputString, true, opt => opt
                //      .WithAudioCodec(FFMpegCore.Enums.AudioCodec.Aac)
                //      .WithDuration(TimeSpan.FromSeconds(17))
                //      .WithCustomArgument("-nostdin"))
                //  .ProcessAsynchronously())
                //{
                //    Task<bool> delay = Task.Delay(TimeSpan.FromSeconds(_deviceMapping.HiSharpRecordTimeoutSeconds)).ContinueWith<bool>((t) => false);
                //    var result = await Task.WhenAny<bool>(task, delay);
                //    if (result == task)
                //    {
                //        returnValue = await task;
                //    }
                //    else
                //    {
                //        await KillFFmpegs();
                //        //Process[] ffmpegProcesses = Process.GetProcessesByName("ffmpeg.exe");

                //        //foreach (Process process in ffmpegProcesses)
                //        //{
                //        //    process.Kill();
                //        //    _logger.LogWarning($"Killed FFmpeg process with PID: {process.Id}");
                //        //}
                //        _logger.LogWarning($"Recording {recordStartingTime.ToString("yyyy/MM/dd HH:mm:ss")}_{camera.Ip} timeout.");
                //    }
                //}
            }
            catch (Exception e)
            {
                await KillExisted();
                _logger.LogError($"Recording exception: {e.Message}");
            }
            
            return returnValue;

        }
        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _cts.Cancel();
            await Task.WhenAll(_tasks);
        }
    }
}
