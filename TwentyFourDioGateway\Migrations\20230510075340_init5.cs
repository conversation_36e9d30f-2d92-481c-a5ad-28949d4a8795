﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    /// <inheritdoc />
    public partial class init5 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ResponseDateTime",
                table: "DawuHistories",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Status",
                table: "DawuHistories",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<byte>(
                name: "TwentyFourDioIndex",
                table: "DawuHistories",
                type: "INTEGER",
                nullable: false,
                defaultValue: (byte)0);

            migrationBuilder.AddColumn<string>(
                name: "TwentyFourDioIp",
                table: "DawuHistories",
                type: "TEXT",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ResponseDateTime",
                table: "DawuHistories");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "DawuHistories");

            migrationBuilder.DropColumn(
                name: "TwentyFourDioIndex",
                table: "DawuHistories");

            migrationBuilder.DropColumn(
                name: "TwentyFourDioIp",
                table: "DawuHistories");
        }
    }
}
