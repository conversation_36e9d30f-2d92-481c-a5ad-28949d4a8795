﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TwentyFourDioGateway.Data;

#nullable disable

namespace TwentyFourDioGateway.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20241122065039_alt_request_name")]
    partial class alt_request_name
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "7.0.5");

            modelBuilder.Entity("TwentyFourDioGateway.Models.DawuHistory", b =>
                {
                    b.Property<ulong>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<byte>("SubnetChannelId")
                        .HasColumnType("INTEGER");

                    b.Property<byte>("TwentyFourDioIndex")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TwentyFourDioIp")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("DawuHistories");
                });

            modelBuilder.Entity("TwentyFourDioGateway.Models.DawuResponse", b =>
                {
                    b.Property<ulong>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("TEXT");

                    b.Property<byte>("SubnetChannelId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("DawuResponses");
                });

            modelBuilder.Entity("TwentyFourDioGateway.Models.Node", b =>
                {
                    b.Property<ushort>("Address")
                        .HasColumnType("INTEGER");

                    b.Property<int>("AddressType")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Value")
                        .HasColumnType("INTEGER");

                    b.HasKey("Address", "AddressType");

                    b.ToTable("Nodes");
                });

            modelBuilder.Entity("TwentyFourDioGateway.Models.RecordingRequest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BaUrl")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EmailReceivers")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsNotificationCompleted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsNotificationSuccessed")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsSuccessed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PublicVideoUrl")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("RecordDurationSeconds")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RecordUrl")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("SendNotification")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SmsReceivers")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT");

                    b.Property<int>("VideoCamId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("RecordingRequests");
                });
#pragma warning restore 612, 618
        }
    }
}
