﻿using Microsoft.EntityFrameworkCore;
using System;
using TwentyFourDioGateway.Models;

namespace TwentyFourDioGateway.Data
{
    public class AppDbContext : DbContext
    {
        public DbSet<Node> Nodes { get; set; }
        public DbSet<DawuHistory> DawuHistories { get; set; }
        public DbSet<DawuResponse> DawuResponses { get; set; }
        public DbSet<RecordingRequest> RecordingRequests { get; set; }
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            //optionsBuilder.UseSqlite("Data Source=mydatabase.db");            
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Node>().<PERSON><PERSON>ey(e => new { e.Address, e.AddressType });
            //modelBuilder.Entity<Node>().Property(p => p.UpdateTime).HasDefaultValueSql
            //    ("CURRENT_TIMESTAMP")
            //    .ValueGeneratedOnAddOrUpdate();
            
        }
    }
}
